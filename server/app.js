const Koa = require('koa'); // 引入koa框架
const path = require('path');
const KoaStatic = require('koa-static');
const Router = require('koa-router');

const { historyApiFallback } = require('koa2-connect-history-api-fallback');

const app = new Koa();
const router = new Router();

const PORT = process.env.PORT || 3000;

app
  // koa2-connect-history-api-fallback中间件必定要放在静态资源服务中间件前面加载
  .use(
    historyApiFallback({
      // whiteList: ['/api'], //设置白名单，访问此url不做拦截
      index: '/index.html'
    })
  )
  // 配置静态资源服务中间件，指定域名根目录的映射
  .use(KoaStatic(path.join(__dirname, '../dist')))
  .use(KoaStatic(path.join(__dirname, '../static')));

// 配置路由
router.get('/', async ctx => {
  ctx.render('index');
});

// 其他配置路由

//使用路由中间件
app.use(router.routes()).use(router.allowedMethods());
app.use(KoaStatic(path.join(__dirname, './')));
app.listen(PORT);
console.log('Listen on http://localhost:' + PORT);
