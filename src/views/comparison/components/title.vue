<template>
  <div class="comparison-title">
    <img class="comparison-title-img" :src="url" />
    <p v-if="intro" class="comparison-title-intro">{{ intro }}</p>
  </div>
</template>
<script>
export default {
  props: {
    url: {
      type: String
    },
    intro: {
      type: String
    }
  }
};
</script>
<style lang="less" scoped>
.comparison-title {
  display: flex;
  align-items: center;
  flex-direction: column;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 20px;
  margin-bottom: 30px;
  .comparison-title-img {
    height: 68px;
  }
  .comparison-title-intro {
    margin-top: 10px;
  }
}
</style>
