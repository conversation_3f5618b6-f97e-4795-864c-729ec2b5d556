<template>
  <div
    class="unit"
    :class="[
      'unit' + unitInfo.level,
      'unit' + unitInfo.bookNodeId,
      unitInfo.status
    ]"
    :level="unitInfo.level"
    @click.stop
  >
    <!-- 本单元内容 -->
    <div
      class="unit-item"
      :textbookId="unitInfo.textbookId && unitInfo.textbookId.join(',')"
      :level="unitInfo.level"
      :banner="
        !isShowAdd && (!unitInfo.children || unitInfo.children.length === 0)
      "
      :isSpecialChapter="unitInfo.isSpecialChapter && selectAll"
      @click="onChecked"
    >
      <!-- 章节名字和被选中的周计划内容 -->
      <div class="unit-item-desc">
        <div class="unit-item-desc-flex">
          <p
            class="unit-item-desc-name"
            :checked="
              (unitInfo.checked || unitInfo.hasOneChildrenChecked) && !isDelete
            "
          >
            <el-tooltip
              effect="dark"
              :content="unitInfo.statusText"
              placement="left"
              popper-class="el-tooltip-warehouse cursor-pointer"
            >
              <span
                class="dot"
                :class="unitInfo.status"
                v-if="
                  (!unitInfo.children || unitInfo.children === 0) && isDelete
                "
              ></span>
            </el-tooltip>
            <span class="bookNodeName" ref="customize"
              >{{ isEditCustomizeText ? textAreaValue : unitInfo.bookNodeName }}
            </span>
          </p>
          <p class="unit-item-desc-info" v-if="unitInfo.parentName">
            {{ unitInfo.parentName }}
          </p>
        </div>
        <!-- 选中 -->
        <template
          v-if="
            isClickAdd && (!unitInfo.children || unitInfo.children.length === 0)
          "
        >
          <el-tooltip
            effect="dark"
            :content="
              `${
                unitInfo.bindPreparationDiscussion &&
                unitInfo.bindPreparationDiscussion.length > 0
                  ? unitInfo.bindPreparationDiscussion[0]
                  : ''
              }`
            "
            placement="top"
            :disabled="!checkIsDisabled() || !checkIsLastLevel(unitInfo)"
          >
            <span>
              <el-checkbox
                class="unit-checkbox"
                v-model="unitInfo.checked"
                :indeterminate="unitInfo.hasOneChildrenChecked"
                :disabled="checkIsDisabled()"
                @click.stop
              ></el-checkbox>
            </span>
          </el-tooltip>
        </template>
      </div>
    </div>
    <!-- 子节点 -->
    <div
      :id="'unit-children' + unitInfo.bookNodeId"
      class="unit-children"
      v-if="
        (selectAll && unitInfo.isSpecialChapter) ||
          isShowAdd ||
          (unitInfo.children && unitInfo.children.length)
      "
    >
      <!-- 子节点展示 -->
      <div
        class="unit-children-sortable"
        :class="['unit-sortable' + unitInfo.bookNodeId + '_' + unitInfo.week]"
      >
        <unit
          v-for="(item, index) in unitInfo.children"
          :key="`${item.bookNodeId}+${index}`"
          :unitInfo="item"
          :lastLevel="
            (!item.children || item.children.length === 0) &&
              !item.isSpecialChapter
          "
          :parentBookNodeNames="
            parentBookNodeNames.concat(unitInfo.bookNodeName)
          "
          :existingCustomNameMap="existingCustomNameMap"
          :existingCustomChapterMap="existingCustomChapterMap"
          :border="isShowBorder"
          :isShowBorder="isShowBorder"
          :isDelete="isDelete"
          :isSortable="isSortable"
          :isClickAdd="isClickAdd"
          :selectAll="selectAll"
          :selectChapter="selectChapter"
          :isSpecialChapter="unitInfo.isSpecialChapter"
          :hasPrepareData="hasPrepareData"
          :isSetMaster="isSetMaster"
          :teachGroupId="teachGroupId"
          :showSettingBtn="showSettingBtn"
          @emitOnDelete="emitOnDelete(item)"
          @emitOnChildrenCheck="emitOnChildrenCheck"
          @emitOnDeleteWeekPlan="emitOnDeleteWeekPlan"
          @emitOnChangeUnit="emitOnChangeUnit"
          @emitCustomChooseChapter="emitCustomChooseChapter"
        ></unit>
        <!-- 特殊章节需要选择 -->
      </div>
    </div>
  </div>
</template>
<script>
import Chapter from '@/object/Chapter';
import Book from '@/object/Book';
import cloneDeep from 'lodash/cloneDeep';
export default {
  name: 'unit',
  inject: ['setUnit'],
  props: {
    subjectSelectObj: {
      type: Object
    },
    // 父节点的 BookNodeName
    parentBookNodeNames: {
      type: Array,
      default: () => []
    },
    // 已存在的自定义课时名称
    // key: JSON.stringify(textbookId) + ':' + chapterId
    existingCustomNameMap: {
      type: Map,
      default: () => new Map()
    },
    // 已存在的自定义章节名称
    // key: JSON.stringify(textbookId) + ':' + chapterName
    existingCustomChapterMap: {
      type: Map,
      default: () => new Map()
    },
    unitInfo: {
      type: Object,
      default: () => {}
    },
    selectChapter: {
      type: Array,
      default: () => []
    },
    // 是否要删除
    isDelete: {
      type: Boolean,
      default: false
    },
    // 是否要滑动
    isSortable: {
      type: Boolean,
      default: false
    },
    // 是否可以点击添加
    isClickAdd: {
      type: Boolean,
      default: false
    },
    // 添加按钮的展示点击权限
    addActivity: {
      type: Boolean,
      default: false
    },
    // 是否需要选择章节
    selectAll: {
      type: Boolean,
      default: false
    },
    isShowAdd: {
      type: Boolean,
      default: false
    },
    isShowBorder: {
      type: Boolean,
      default: false
    },
    hasPrepareData: {
      type: Map,
      default: () => new Map()
    },
    // 是否可以设置主备人
    isSetMaster: {
      type: Boolean,
      default: false
    },
    teachGroupId: {
      type: String,
      default: ''
    },
    showSettingBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      selectAdd: null,
      textAreaValue: '', // 自定义文案
      isEditCustomizeText: false, //是否在编辑自定义文案
      editCustomizeTextStyle: {}, // 自定义文案的编辑样式
      screenCallbackFn: null // 页面尺寸变化监听的方法
    };
  },
  computed: {
    activityName() {
      return (
        this.unitInfo?.bindPreparationDiscussion &&
        this.unitInfo.bindPreparationDiscussion[0]?.preparationDiscussionName
      );
    }
  },
  mounted() {
    // 监听屏幕变化，仅自定义文案
    this.screenCallbackFn = this.$screenCallback.bind(
      null,
      this.onListenerScreenChange
    );
    this.unitInfo.isCustomized &&
      this.$addScreenListener(this.screenCallbackFn);

    this.textAreaValue =
      this.unitInfo.isCustomized && this.unitInfo.bookNodeName;
  },
  watch: {
    selectAdd(val) {
      if (val) {
        const unitIndex = this.unitInfo.options.findIndex(
          item => item.bookNodeId === val
        );
        if (unitIndex > -1) {
          const unit = this.unitInfo.options[unitIndex];
          this.unitInfo.options.splice(unitIndex, 1);
          unit.level = this.unitInfo.level + 1;
          this.unitInfo.addChild(unit);
          this.$emit('emitCustomChooseChapter', this.unitInfo);
          this.selectAdd = null;
        }
      }
    },
    unitInfo: {
      handler() {
        this.$forceUpdate();
        // this.$log.info('🔥🔥🔥', this.unitInfo);
      },
      deep: true
    },
    textAreaValue() {
      // 计算弹出框的高度
      const minHeight = '40px';
      const textarea = this.$refs.customizeEdit;
      if (!textarea) {
        return;
      }
      textarea.style.height = minHeight;
      if (this.textAreaValue) {
        let scrollHeight = textarea.scrollHeight;
        const style = getComputedStyle(textarea);
        const borderTop = parseInt(style.borderTop);
        const borderBottom = parseInt(style.borderBottom);
        const height = scrollHeight + borderTop + borderBottom;
        this.$refs.customizeEdit.style.height = height + 'px';
      }
    }
  },
  methods: {
    emitOnChangeUnit(params) {
      this.$emit('emitOnChangeUnit', params);
    },
    emitCustomChooseChapter(data) {
      this.$emit('emitCustomChooseChapter', data);
    },

    /**
     * 这个单元已经是最后一个啦，不能删的
     * @param {*} unitInfo
     */
    getIsLastChapter(unitInfo) {
      const weekInfo = this.hasPrepareData.get(unitInfo.getFrontKey()) || [];
      if (weekInfo.size === 1) {
        this.$message({
          message: '该单元正在使用中，无法删除！',
          type: 'warning',
          duration: 2 * 1000
        });
        return true;
      }
      return false;
    },

    async emitOnDelete(unitInfo) {
      try {
        const index = this.unitInfo.children.findIndex(
          item => item.bookNodeId === unitInfo.bookNodeId
        );
        if (unitInfo instanceof Chapter) {
          if (this.getIsLastChapter(unitInfo)) return;
        }
        if (
          this.unitInfo instanceof Chapter &&
          this.unitInfo.children.length <= 1
        ) {
          if (this.getIsLastChapter(this.unitInfo)) return;
        }
        if (index > -1) {
          // 如果有子节点的话, 说明是在这一层的
          // 先通知要被删除了
          const isDeleted = await this.unitInfo.deleteChild(index);
          if (!isDeleted) {
            return;
          }
          if (this.unitInfo instanceof Book) {
            this.$emit('emitOnDeleteWeekPlan', this.unitInfo, unitInfo);
          }
          if (
            this.unitInfo.children.length === 0 &&
            !this.unitInfo.isSpecialChapter
          ) {
            // 通知父节点已经被点击
            this.$emit('emitOnDelete', this.unitInfo);
          }
        }
      } catch (error) {
        this.$log.warn('emitOnDelete', error);
      }
    },

    emitOnDeleteWeekPlan() {
      this.$emit('emitOnDeleteWeekPlan', this.unitInfo);
    },

    onChecked() {
      if (!this.isClickAdd) return;
      if (this.checkIsDisabled()) return;
      this.$log.info(this.unitInfo.checked);
      this.unitInfo.checked = !this.unitInfo.checked;
      this.$log.info(this.unitInfo.checked);
      this.unitInfo.children &&
        this.unitInfo.changeAllUnitsStatus(this.unitInfo.checked);
      this.setUnit(
        this.unitInfo.children && this.unitInfo.children.length > 0
          ? this.unitInfo.getAllTile()
          : [this.unitInfo]
      );

      // 通知父节点已经被点击
      if (this.unitInfo instanceof Chapter) {
        this.emitOnChildrenCheck();
      } else {
        this.$emit('emitOnChildrenCheck');
      }
    },
    /**
     * 子节点触发，计算子节点是否全部被选中, 子节点
     */
    emitOnChildrenCheck() {
      if (!this.isClickAdd) return;
      this.unitInfo.checkUpChildrenChecked();
      // 获取所有被选中的子节点
      // 深拷贝unitInfo
      // 生成新的章节数据，传递到父节点
      let unitInfoCopy = cloneDeep(this.unitInfo);
      let chapters = unitInfoCopy.getHasCheckedChildrenList();
      unitInfoCopy.children = chapters;
      // 如果子节点数量>0,说明需要添加
      // 如果子节点数量=0, 说明需要删除
      this.$emit('emitOnChildrenCheck', unitInfoCopy);
    },

    // 修改自定义标签的状态
    onChangeEditCustomizeType() {
      this.isEditCustomizeText = true;
      this.onListenerScreenChange();
      this.textAreaValue = this.unitInfo.bookNodeName;
    },
    /**
     * 更新组件宽度
     */
    onListenerScreenChange() {
      if (!this.isEditCustomizeText) {
        return;
      }
      this.editCustomizeTextStyle = {
        width: `${this.$refs.customize.scrollWidth}px`,
        // 20， 为了UI妥协的padding
        height: `${this.$refs.customize.scrollHeight + 20}px`,
        left: `${this.$refs.customize.offsetLeft - 2}px`
      };
    },

    /**
     * 修改自定义的计划文案
     */
    onKeydown($event) {
      // this.$log.info($event);
      if ($event.key === 'Enter') $event.preventDefault();
    },
    // 修改自定义的名称
    onChangeBookNodeName() {
      this.isEditCustomizeText = false;
      if (!this.textAreaValue.trim()) {
        return;
      }

      // -------- 检测自定义课时名称是否重名 --------
      const { parentBookNodeNames, textAreaValue } = this;
      const { textbookId } = this.unitInfo;
      // 周计划的名称 weekPlan.bookNodeName
      const weekPlanName = parentBookNodeNames[0];
      // 章节的名称 chapter.bookNodeName
      const upperLevelBookNodeName =
        parentBookNodeNames[parentBookNodeNames.length - 1];
      const isRenamed = this.detectingRename(
        weekPlanName,
        textbookId,
        upperLevelBookNodeName,
        textAreaValue
      );

      if (isRenamed) return;
      // -------- 检测自定义课时名称是否重名 --------

      this.unitInfo.bookNodeName = this.textAreaValue;
    },

    checkIsLastLevel(item) {
      return !item.children || item.children.length === 0;
    },

    checkIsDisabled() {
      const len =
        this.unitInfo.bindPreparationDiscussion &&
        this.unitInfo.bindPreparationDiscussion.length;
      return len !== 0 && len >= (this.unitInfo.children || []).length;
    }
  },
  destroyed() {
    /**
     * 移除监听页面变化事件
     */
    window.removeEventListener('resize', this.screenCallbackFn);
  }
};
</script>
<style lang="less" scoped>
.changeColor(level) {
}
.line {
  position: absolute;
  bottom: 0px;
  left: 0px;
  margin: 0;
  width: 100%;
  height: 1px;
  background: #efefef;
  content: '';
}

.unit {
  display: flex;
  align-items: stretch;
  flex-grow: 1;
  text-align: center;

  &-item {
    position: relative;
    display: flex;
    align-items: center;
    flex: 0 0 110px;
    justify-content: space-between;
    box-sizing: border-box;
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 14px 0 14px 18px;
    min-height: 50px;
    width: 110px;
    border-radius: 10px;
    background: #fff;
    word-break: break-all;
    line-height: 20px;
    cursor: pointer;

    user-select: none;
    &-edit {
      position: absolute;
      top: 50%;
      left: 30px;
      overflow-y: hidden;
      box-sizing: border-box;
      margin: 0;
      padding: 10px;
      border: none;
      border-radius: 8px;
      background: #f2f2f2;
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      line-height: 20px;
      resize: none;
      transform: translateY(-50%);
    }
    .bookNodeName {
      flex: 1;
      padding-right: 20px;
    }
    /deep/ .el-checkbox__inner {
      transition: 0.3s;
    }
    &:hover /deep/ .el-checkbox__input {
      &:not(.is-disabled) .el-checkbox__inner {
        border-color: @theme-color;
      }
    }

    &[level='1'] {
      flex: 0 0 200px;
    }
    &[banner] {
      flex: 1 !important;
      margin-right: 0px;
    }
    &[isSpecialChapter][level='1'] {
      flex: 0 0 200px !important;
      margin-right: 10px;
    }
    &-desc {
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.65);
      text-align: left;
      font-size: 14px;
      &-flex {
        flex: 1;
      }
      .master-box {
        display: flex;
        flex: 0 0 70px;
        justify-content: end;
        .partner-info {
          width: auto;
        }
      }
      &-name {
        display: flex;
        align-items: center;
        &[checked] {
          color: @menuActiveTxtColor;
        }
        & .dot {
          display: inline-block;
          flex-shrink: 0;
          margin-right: 7px;
          width: 8px;
          height: 8px;
          border-radius: 100%;
        }
        & .wait {
          background: rgba(128, 118, 110, 1);
        }
        & .doing {
          background: rgba(250, 126, 35, 1);
        }

        .finish {
          background: rgba(34, 148, 83, 1);
        }
      }
      &-info {
        padding-top: 5px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
        line-height: 17px;
      }
    }
  }
  &-children {
    display: flex;
    overflow: hidden;
    align-items: stretch;
    flex: 1 1 250px;
    flex-direction: column;
    border-radius: 10px;
    & .unit-item[level='1'][border] {
      border: 2px solid #fff;
    }
    // 为了level2层级的样式，特殊处理
    & [lastLevel] .unit-item {
      position: relative;
      margin-bottom: 0px;
      border: none;
      border-radius: 0px;
      &:before {
        .line;
      }
    }
    & .unit[lastLevel]:last-child {
      & .unit-item {
        margin-bottom: 10px;
        border-bottom-right-radius: 10px;
        border-bottom-left-radius: 10px;
        &:before {
          height: 0px;
        }
      }
    }
    & .unit[lastLevel]:only-child {
      & .unit-item {
        &:before {
          height: 0px !important;
        }
      }
    }
    & .unit[lastLevel][isSpecialChapter]:last-child {
      & .unit-item {
        margin-bottom: 0px;
        border-bottom: none;
        border-bottom-right-radius: 0px;
        border-bottom-left-radius: 0px;
        &:before {
          height: 1px !important;

          .line;
        }
      }
    }
    & .unit[lastLevel]:first-child {
      & .unit-item {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        &:before {
          .line;
        }
      }
    }
    & .unit[isFirst] {
      & .unit-item {
        border-top: 2px solid #fff;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
      }
    }
    // & [border] {
    & .unit[level='1'][border] {
      & > .unit-item {
        border-top: 2px solid #fff;
        border-bottom: 2px solid #fff;
      }
    }
    // }
    & [border] .unit-item {
      border-right: 2px solid #fff;
      border-left: 2px solid #fff;
    }
    & .unit[lastLevel][border]:first-child {
      & .unit-item {
        border-top: 2px solid #fff;
      }
    }
    & .unit[lastLevel][border]:last-child {
      & .unit-item {
        border-bottom: 2px solid #fff;
      }
    }
    & .unit[lastLevel][isSpecialChapter][border]:last-child {
      & .unit-item {
        border-bottom: none;
        &:before {
          .line;
        }
      }
    }
    // & .unit[border]:first-child {
    //   & .unit-item {
    //     border-top: 2px solid #fff;
    //   }
    // }
    // & .unit[border]:last-child {
    //   & .unit-item {
    //     border-bottom: 2px solid #fff;
    //   }
    // }

    &-sortable:only-child {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    /deep/ .el-select {
      display: block;
      min-height: 48px;
      width: 100%;
      &.unit-item {
        padding: 0;
      }
    }
    /deep/ .el-input.el-input--suffix {
      height: 100%;
    }
    /deep/ .el-input__inner {
      height: 100%;
      border: none;
      background: transparent;
    }
  }
  &-add {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    justify-content: center;
    box-sizing: border-box;
    margin-bottom: 10px;
    padding-left: 18px;
    height: 50px;
    border: 2px dashed rgba(0, 0, 0, 0.45);
    border-radius: 10px;
    background: rgba(248, 248, 248, 1);
    color: rgba(0, 0, 0, 0.45);
    text-align: left;
    font-size: 14px;
    transition: 0.3s;
    & i {
      padding-right: 8px;
      font-size: 18px;
    }
    &:hover {
      border-color: @menuActiveTxtColor;
      color: @menuActiveTxtColor;
    }

    &[error] {
      border-color: #f34718;
    }

    /deep/ .el-button {
      position: relative;
      margin: 0;
      padding: 14px;
      color: rgba(0, 0, 0, 0.45);
      & .iconfont {
        font-size: 14px;
      }
      &:hover {
        color: @theme-color;
      }
      &.is-disabled {
        color: rgba(0, 0, 0, 0.25);
      }
      &:last-child {
        padding-right: 20px;
        padding-left: 16px;
      }
      &:last-child:before {
        position: absolute;
        top: 50%;
        left: 0px;
        margin: 0;
        width: 1px;
        height: 10px;
        background: rgba(0, 0, 0, 0.45);
        content: '';
        transform: translate(0%, -50%);
      }
    }
  }
  &-setting {
    display: flex;
    align-content: center;
    flex: 0 0 66px;
    justify-content: right;
    height: 20px;
  }

  &-delete {
    padding: 0 6px;
    color: #d5d8de;
    font-size: 20px;
    transition: 0.3s;
    &:last-child {
      padding-right: 12px;
    }
    &:first-child {
      padding-left: 10px;
    }
  }

  &-custom {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    /deep/ .el-select {
      position: relative;
      display: block;
      margin-right: 22px;
      padding-right: 5px;
      width: 180px;
      &.unit-item {
        padding: 0;
      }
      &[error] {
        .el-input__inner::placeholder {
          color: #f34718 !important;
        }
      }
      &::after {
        position: absolute;
        top: 50%;
        right: 0;
        width: 1px;
        height: 10px;
        border-radius: 1px;
        background: rgba(0, 0, 0, 0.25);
        content: '';
        transform: translate(0, -50%);
      }
    }
    /deep/ .el-input.el-input--suffix {
      height: 50px;
    }
    /deep/ .el-input__icon {
      &::before {
        color: rgba(0, 0, 0, 0.65);
        font-size: 12px;
      }
    }
    /deep/ .el-input__inner {
      padding-left: 0;
      height: 50px;
      border: none;
      background: transparent;
    }

    &-item {
      position: absolute;
      z-index: 1;
      width: 150px;
      background: #f8f8f8;
      color: rgba(0, 0, 0, 0.65);
      line-height: 46px;
      pointer-events: none;

      .ellipsis;
      &:hover {
        color: rgba(0, 0, 0, 0.45);
      }
    }

    &-input {
      display: flex;
      align-items: center;
      flex: 1;
      & input {
        width: 100%;
        height: 100%;
        border: none;
        background: transparent;
        color: rgba(0, 0, 0, 0.65);
        &::placeholder {
          color: rgba(0, 0, 0, 0.25);
        }
      }
      &[error] {
        & .unit-custom-delete {
          color: #f34718;
        }
        & input::placeholder {
          color: #f34718;
        }
      }
    }
    &-delete {
      color: @theme-color;
      font-size: 20px;
      cursor: pointer;
      & i {
        padding-right: 9px;
        font-size: 20px;
      }
    }
  }

  &-checkbox {
    padding-right: 20px;
    padding-left: 10px;
    pointer-events: none;
  }
}
/deep/ .el-select .el-input .el-select__caret {
  margin-right: 2px;
  transform: rotateZ(0deg);
  transform-origin: center center;

  &.is-reverse {
    transform: rotateZ(180deg);
  }
}

/deep/ .el-input__suffix-inner .el-icon-arrow-up::before {
  content: '\e7ae';
  font-size: 6px;
  transform: unset;
}
</style>
