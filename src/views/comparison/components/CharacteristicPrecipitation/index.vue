<!-- 特色沉淀 -->
<template>
  <div class="file">
    <div class="file-box" v-for="(item, index) in fileList" :key="index">
      <div
        class="file-item"
        :class="{
          'has-file-item': item.pubResourceUrl,
          'is-disabled': disabled && !item.pubResourceUrl,
          'file-disabled': !teachGroupId
        }"
      >
        <!-- 无文件 start -->
        <div v-if="!item.pubResourceUrl && !disabled" class="no-file">
          <b-file-upload
            v-show="!item.uploadStatus"
            :ref="`bFileUploadRef_${index}`"
            :limit="1"
            :maxSize="1024 * 1024 * 1024"
            :usePreset="false"
            :acceptDrag="false"
            :accepts="acceptStr"
            :disabled="!teachGroupId"
            uploadInfoType="Progress"
            @setCurUploadData="setCurUploadData"
            @noPermission="file => onResourceAdd(item, file)"
            @change="list => onResourceAdd(item, list[0])"
          >
            <template v-slot:uploadBtn>
              <div class="upload-box">
                <i class="iconfont icon-icon-common-add"></i>
                <span>上传文件</span>
              </div>
            </template>
          </b-file-upload>
        </div>
        <!-- 无文件 end -->

        <!-- 有文件 start -->
        <div
          v-if="item.pubResourceUrl"
          class="has-file"
          @click.stop="onPreview(item)"
        >
          <div class="has-file-left">
            <img class="file-icon" :src="countFileExtension(item)" alt="" />
            <div class="file-text">
              <el-tooltip
                effect="dark"
                :content="item.pubResourceName"
                placement="top"
                :disabled="!isShowTooltipFlag"
              >
                <div class="file-name" @mouseenter="onJudgeShowTooltip($event)">
                  {{ item.pubResourceName }}
                </div>
              </el-tooltip>
              <!-- 有文件名字说明是新上传的文件，否则是反显数据 -->
              <div class="file-size">
                {{ item.name ? item.size : fileSize(item.size) }}
              </div>
            </div>
          </div>
          <i
            class="iconfont icon-icon-common-ashbin"
            @click.stop="onDelFile(item)"
            v-if="!disabled"
          ></i>
        </div>
        <!-- 有文件 end -->
      </div>

      <img
        v-if="item.level === 1"
        class="file-tag"
        src="~@/assets/images/comparison/level1.png"
      />
    </div>
    <!-- 上传文件进度加载页 start -->
    <files-uploading
      v-if="openUploading && progressList.length > 0"
      :fileList="progressList"
      @onCloseUploading="openUploading = false"
    ></files-uploading>
    <!-- 上传文件进度加载页 end -->
  </div>
</template>

<script>
import BFileUpload from '@/components/business/BFileUpload';
import getSize from '@/plugins/common/getSize';
import compareText from '@/plugins/common/compareText';
import BFilePreview from '@/components/business/BFilePreview/index.js';
import FilesUploading from '@/views/prepare-main/modules/files-uploading/index.vue';

export default {
  components: {
    BFileUpload,
    FilesUploading
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    teachGroupId: {
      type: String,
      default: ''
    },
    maxFileNum: {
      type: Number,
      default: 3
    }
  },
  data() {
    return {
      // 当前上传文件列表
      fileList: [],
      openUploading: false, // 展示uploading弹窗
      isShowTooltipFlag: false, // 优秀作业内容是否展示tooltip
      // 支持上传文件类型
      acceptStr: [
        'txt',
        'doc',
        'docx',
        'ppt',
        'pptx',
        'pdf',
        'jpg',
        'jpeg',
        'png',
        'mp4',
        'mp3',
        'wav',
        'xls',
        'xlsx',
        'zip',
        'rar',
        'm4a',
        'ogg',
        'wmv'
      ]
    };
  },
  mounted() {
    this.initFileList();
  },
  computed: {
    /**
     * 计算展示文件大小
     */
    fileSize() {
      return row => {
        return getSize(row);
      };
    },
    /**
     * 计算展示文件类型图标
     */
    countFileExtension() {
      return row => {
        return require('@/assets/images/files/' +
          this.$util.judgeFileExtension(row.pubResourceName) +
          '-icon.png');
      };
    },
    /**
     * 文件上传进度列表
     */
    progressList() {
      let list = [];
      list = this.fileList.filter(
        item => item.uploadStatus && !item.pubResourceUrl
      );
      return list;
    }
  },
  methods: {
    /**
     * 初始化文件列表
     */
    initFileList() {
      this.fileList = this.maxFileNum
        ? Array.from({ length: this.maxFileNum }, () => ({}))
        : [];
    },
    /**
     * 是否提示toolTip
     */
    onJudgeShowTooltip(event) {
      this.isShowTooltipFlag = compareText(event);
    },
    /**
     * 资源文件预览
     */
    onPreview(file) {
      if (!file.pubResourceUrl) return;
      let list = this.fileList.filter(item => item.pubResourceUrl);
      BFilePreview({
        url: file.pubResourceUrl,
        name: file.pubResourceName,
        tip: '该文件暂无在线预览',
        // 加上这个就可以左右切换预览文件了
        fileList: list.map(item => ({
          name: item.pubResourceName || item.name,
          url: item.pubResourceUrl
        }))
      });
    },
    /**
     * 上传资源状态
     */
    onResourceAdd(item, file) {
      this.$log.info('上传资源状态111:', item, file, file.status);
      switch (file.status) {
        case 'ready':
          // ready 状态
          this.$set(item, 'pubResourceName', file.name);
          this.$set(item, 'uploadStatus', 'ready');
          this.$set(item, 'uid', file.uid);
          this.openUploading = true;
          break;
        case 'refuse':
          // 在校验阶段被拦下了
          this.$set(item, 'pubResourceName', '');
          this.$set(item, 'uploadStatus', '');
          this.$set(item, 'uid', '');
          break;
        case 'success':
          // 已上传好
          this.$set(item, 'pubResourceUrl', file.url);
          this.$set(item, 'pubResourceFormat', this.getFileSuffix(file.name));
          this.$set(item, 'size', this.fileSize(item.transferredAmount));

          // 更新文件数据之后的操作
          this.openUploading = false;
          this.updateFileList();
          break;
        case 'fail':
          // 上传失败
          this.$set(item, 'uploadStatus', 'fail');
          this.$set(item, 'failType', 0); // 表示在图片上传到oss这步就挂了
          this.$set(item, 'failFile', file.raw); // 记录下以便再次上传
          this.$message.warning('网络异常，请检查网络后刷新再试');
          break;
        default:
          break;
      }
    },
    /**
     * 更新文件数据之后的操作
     */
    updateFileList() {
      this.$log.info('🍉 🍉 🍉更新文件数据之后的操作', this.fileList);
      this.$emit('emitUpdateData', this.fileList);
    },
    /**
     * 反显数据设置
     */
    setFlieList(list) {
      this.$log.info('反显数据设置', list);
      if (list && list.length > 0) {
        this.fileList = list;
        if (list.length !== this.maxFileNum) {
          this.onRecursionlist();
          // this.$log.info('🍉 🍉 ', this.fileList);
        }
        this.$emit('emitUpdateData', this.fileList);
      } else {
        this.onClearFlieList();
      }
    },
    // 递归补齐fileList
    onRecursionlist() {
      this.fileList.push({ pubResourceName: '', uploadStatus: '' });
      if (this.fileList.length < this.maxFileNum) {
        this.onRecursionlist();
      } else {
        return;
      }
    },
    /**
     * 清空文件列表
     */
    onClearFlieList() {
      this.initFileList();
      this.$log.info('🔋 给我空空空', this.fileList);
    },
    /**
     * 计算上传速率，并实时同步到每个文件
     * @param {*} data
     * progress 进度
     * totalAmount 文件总小大
     * totalSeconds 已消耗时间
     * transferredAmount 已上传大小
     */
    setCurUploadData(data) {
      this.openUploading = true;
      // 上传进度，百分比，不保留小数
      let progress = parseInt((data.progress * 100).toFixed(0));
      let index = this.fileList.findIndex(item => item.uid === data.uid);
      let itemData = this.fileList[index];
      // 需要修改的当前文件信息
      const option = {
        ...itemData,
        transferredAmount: data.transferredAmount,
        totalSeconds: data.totalSeconds,
        progress,
        name: itemData.pubResourceName,
        status: itemData.uploadStatus,
        uploadRate: itemData.uploadRate || 0
      };
      // 上传速率 = (上行传输大小 - 下行传输大小) / 时间差
      // 传输资源大小差
      let amount = itemData.transferredAmount
        ? data.transferredAmount - itemData.transferredAmount
        : data.transferredAmount;
      // 传输时间差
      let seconds = itemData.totalSeconds
        ? data.totalSeconds - itemData.totalSeconds
        : data.totalSeconds;
      // 修改速率，间隔时差
      let stepTime = 100;
      let time = setTimeout(() => {
        // 改变上传速率，保留两位小数
        option.uploadRate = amount && getSize(amount / seconds);
        // // 传输文件大小差
        // option.amount = data.transferredAmount;
        // // 传输时间差
        // option.seconds = data.totalSeconds;
        clearTimeout(time);
      }, stepTime);
      this.$set(this.fileList, index, option);
      this.$log.info('🆒 当前上传进度：' + progress, option.uploadRate, option);
      // this.$log.info('🆒 文件信息', data, this.fileList);
    },
    /**
     * 文件后缀名
     */
    getFileSuffix(data) {
      let extIndex = data.lastIndexOf('.');
      let ext = data.slice(extIndex + 1).toLocaleLowerCase();
      return ext;
    },
    /**
     * 删除文件
     */
    onDelFile(data) {
      this.$confirm('是否确认移除该资源？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        customClass: 'del-confirm-box',
        confirmButtonClass: 'confirm-btn'
      })
        .then(_ => {
          this.$set(data, 'pubResourceUrl', '');
          this.$set(data, 'uploadStatus', '');
          this.$set(data, 'progress', 0);
          this.updateFileList(); // 更新暂存数据
          this.$log.info('删除文件', data, 'fileList:', this.fileList);
        })
        .catch();
    }
  }
};
</script>

<style lang="less" scoped>
.file {
  margin-top: 40px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 20px;
  .file-box {
    padding-top: 10px; // 为了标签能够露出
    position: relative;
    overflow: hidden; // 还是要这个 不然每一格的长度不一样

    .file-tag {
      height: 20px;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  &-item {
    height: 72px;
    box-sizing: border-box;
    border-radius: 10px;
    border: 2px dashed rgba(0, 0, 0, 0.15);
    &:hover {
      border-color: #5c6bd0;
    }
    .upload-box {
      height: 72px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      .icon-icon-common-add {
        margin-right: 6px;
        font-size: 13px;
      }
      &:hover {
        color: #5c6bd0;
      }
    }
  }
  .is-disabled {
    opacity: 0;
  }
  .has-file-item {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    cursor: pointer;
    &:hover {
      border-color: #5c6bd0;
    }
    .has-file {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .has-file-left {
        flex: 1;
        display: flex;
        align-items: center;
        max-width: 85%;
        overflow: hidden;

        .file-icon {
          width: 40px;
          height: 40px;
          margin-left: 15px;
        }
        .file-text {
          overflow: hidden;
          .file-name {
            padding-left: 8px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 24px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: break-all;
          }
          .file-size {
            padding-left: 8px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            line-height: 16px;
          }
        }
      }
      .icon-icon-common-ashbin {
        padding: 4px;
        margin-right: 15px;
        font-size: 12px;
        background: #f8f8f8;
        border-radius: 5px;
        &:hover {
          background-color: #e6e6e6;
        }
      }
    }
  }
  .file-disabled {
    cursor: not-allowed !important;
    &:hover {
      border-color: #e0e0e0;
      .upload-box {
        cursor: not-allowed !important;
        color: rgba(0, 0, 0, 0.45) !important;
      }
    }
    .upload-icon {
      color: #bbb !important;
    }
    /deep/ .el-upload-dragger {
      border-color: #dcdfe6 !important;
      cursor: not-allowed;
      &:hover {
        border-color: #dcdfe6 !important;
      }
    }
  }
}
</style>
<style lang="less">
.del-confirm-box {
  .el-message-box__message {
    color: rgba(0, 0, 0, 0.45);
  }
}
</style>
