<template>
  <el-dialog
    title="集备资源"
    :visible.sync="visible"
    width="700px"
    :before-close="onClose"
    :append-to-body="true"
    :close-on-click-modal="false"
    custom-class="choose-unit-dialog"
  >
    <div v-loading="isLoading">
      <!-- 选择器 start -->
      <section class="choose-unit-header">
        <div>
          学期计划：<!--
      --><el-select
            class="choose-unit-header-plan"
            v-model="teachPlanId"
            placeholder="请选择"
            popper-class="hl-selectgroup-max-height-360 select-scroll-bar select-list-item"
          >
            <el-option
              v-for="item in teachPlanList"
              :key="item.teachPlanId"
              :label="item.planName"
              :value="item.teachPlanId"
            >
            </el-option>
          </el-select>
        </div>
        <div>
          备课组：<!--
      --><el-select
            v-model="prepareGroupId"
            placeholder="请选择"
            class="choose-unit-header-group"
            popper-class="hl-selectgroup-max-height-360 select-scroll-bar select-list-item"
          >
            <el-option
              v-for="item in prepareGroupList"
              :key="item.groupId"
              :label="item.groupName"
              :value="item.groupId"
            >
            </el-option>
          </el-select>
        </div>
      </section>
      <!-- 选择器 end -->

      <!-- 学期规划 start -->
      <!-- TODO   空状态 start -->
      <!-- 空状态 end -->
      <section class="choose-unit-main">
        <header class="select-group">
          <span class="level_1">周次</span>
          <span class="level_2">单元</span>
          <span class="level_3">教学内容</span>
        </header>
        <section class="select-content" v-if="planList.length !== 0">
          <Unit
            v-for="item in planList"
            :key="item.bookNodeId"
            :unitInfo="item"
            :isClickAdd="true"
            :teachGroupId="teachGroupId"
            :showSettingBtn="false"
          ></Unit>
        </section>
        <section
          class="select-content-empty"
          v-if="!isLoading && planList.length === 0"
        >
          <EmptyPage tip="暂无资源" :type="5"> </EmptyPage>
        </section>
      </section>
    </div>
    <!-- 学期规划 end -->
    <section slot="footer" class="choose-unit-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" @click="onSubmitUnit"
        >完成选择（{{ choosePlanList.length }}/{{ maxCount }}）</el-button
      >
    </section>
  </el-dialog>
</template>
<script>
import { getTeachGroupList } from '@/api/comparison/index';
import { getTeachPlanByTeachGroup } from '@/api/prepare-plan-setting';
import { getPreparePlanInfo } from '@/api/prepare-plan-setting';
import EmptyPage from '@/components/business/EmptyPage';
import Book from '@/object/Book';
import Unit from '../components/unit';
import { cloneDeep } from 'lodash';

export default {
  components: {
    Unit,
    EmptyPage
  },
  provide() {
    return {
      setUnit: this.setUnit
    };
  },
  props: {
    value: {
      type: Boolean
    },
    activityInfo: {
      type: Object
    },
    teachGroupId: {
      type: String
    },
    maxCount: {
      type: Number,
      default: 0
    }
  },
  watch: {
    disabled: {
      type: Boolean,
      default: false
    },
    teachGroupId() {
      this.ajaxGetTeachPlanlist();
      this.prepareGroupId = '';
    },
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
    teachPlanId(val) {
      this.initReloadPlanDetails();
      val && this.ajaxGetPrepareGrouplist();
    },
    prepareGroupId(val) {
      this.initReloadPlanDetails();
      if (val) {
        this.ajaxGetTeachPlanDatails();
        try {
          this.prepareGroupName = this.prepareGroupList.find(
            item => item.groupId === val
          ).groupName;
        } catch (error) {}
      }
    }
  },

  data() {
    return {
      isLoading: true,
      visible: this.value,
      teachPlanList: [],
      prepareGroupList: [],
      planList: [],
      teachPlanId: '',
      prepareGroupId: '',
      prepareGroupName: '',
      choosePlanList: []
    };
  },
  methods: {
    onClose() {
      this.visible = false;
    },
    initReloadPlanDetails() {
      this.isLoading = true;
      this.planList = [];
    },
    async ajaxGetTeachPlanlist() {
      if (!this.teachGroupId) {
        return;
      }
      try {
        this.teachPlanId = '';
        const { data } = await getTeachPlanByTeachGroup({
          groupId: this.teachGroupId,
          startTime: this.activityInfo?.planStartTime,
          endTime: this.activityInfo?.planEndTime
        });
        this.teachPlanList = data.teachPlanList;
        if (this.teachPlanList.length === 0) {
          this.isLoading = false;
          this.teachPlanId = '';
        }
        this.teachPlanId = this.teachPlanList[0].teachPlanId;
      } catch (error) {
        this.isLoading = false;
        this.$log.warn('ajaxGetTeachPlanlist error', error);
      }
    },
    async ajaxGetPrepareGrouplist() {
      try {
        this.prepareGroupList = [];
        this.prepareGroupId = '';
        const { data } = await getTeachGroupList({
          allowAdmin: true,
          groupType: 'prepare',
          teachPlanId: this.teachPlanId,
          groupIdList: [this.teachGroupId]
        });
        if (data.groupManageList.length > 0) {
          if (data.groupManageList[0].prepareGroupList.length === 0) {
            this.isLoading = false;
            return;
          }
          this.prepareGroupList = data.groupManageList[0].prepareGroupList;
          if (
            this.prepareGroupId &&
            this.prepareGroupId === this.prepareGroupList[0].groupId
          ) {
            this.ajaxGetTeachPlanDatails();
          } else {
            !!this.prepareGroupList.length &&
              (this.prepareGroupId = this.prepareGroupList[0].groupId);
          }
        } else {
          this.isLoading = false;
        }
      } catch (error) {
        this.isLoading = false;
        this.$log.warn('ajaxGetPrepareGrouplist error', error);
      }
    },
    async ajaxGetTeachPlanDatails() {
      try {
        const params = {
          teachPlanId: this.teachPlanId,
          teachGroupId: this.teachGroupId,
          prepareGroupId: this.prepareGroupId
        };
        const { data } = await getPreparePlanInfo(params);
        this.termPlanRequestEnd = true;
        const { teachPreparePlanList } = data;
        const result = Book.simFormatBookObj(teachPreparePlanList);
        this.planList = result.filter(item => item);
        this.changePlanList(this.getChooseIdList());
        this.$log.info('🔥', result, this.getChooseIdList());
      } catch (error) {
        this.$log.warn('ajaxGetTeachPlanDatails error', error);
      } finally {
        this.isLoading = false;
      }
    },
    // 根据得到的集备内容id处理planList的checked状态
    changePlanList(idList) {
      if (!idList.length || !this.planList.length) {
        return;
      }
      this.planList.forEach(item => {
        idList.forEach(unit => {
          // 获取单元
          const result = item.getUnitById(unit);
          if (result) {
            result.checked = true; // 设置选择状态
          }
        });
        item.checkUpChildrenChecked();
      });
    },
    /**
     * 设置unit
     */
    setUnit(data) {
      let isOver = false;
      this.$log.info('🌹🌹🌹🌹🍷🍷🍷🍷', data);
      if (Array.isArray(data)) {
        data.forEach(item => {
          if (!this.handlerPrepareUnitData(item)) {
            isOver = true;
          }
        });
      } else {
        if (!this.handlerPrepareUnitData(data)) {
          isOver = true;
        }
      }
      isOver && this.$message.warning(`单次最多选择${this.maxCount}个教学内容`);
    },
    handlerPrepareUnitData(data) {
      const index = this.choosePlanList.findIndex(unit => unit.id === data.id);
      data.preparationName = `${data.bookInfo.saasSubjectName}/${data.bookInfo.textBookName}/${data.unit.content}/${data.bookNodeName}`;
      data.prepareGroupName = this.prepareGroupName;
      if (data.checked) {
        if (this.choosePlanList.length < this.maxCount) {
          index === -1 && this.choosePlanList.push(data);
        } else {
          data.checked = false;
          this.deletePlanItem(index);
          return false;
        }
        return true;
      }
      this.deletePlanItem(index);
      return true;
    },

    deletePlanItem(index) {
      index > -1 && this.choosePlanList.splice(index, 1);
    },
    /**
     * 获取选中的id列表
     */
    getChooseIdList() {
      const ids = [];
      this.choosePlanList.forEach(item => {
        ids.push(item.id);
      });
      return ids;
    },
    /**
     * 提交
     */
    onSubmitUnit() {
      this.$emit('emitSetChooseContentList', cloneDeep(this.choosePlanList));
      this.$emit('emitSetChooseContent', cloneDeep(this.getChooseIdList()));
      this.visible = false;
    },
    /**
     * 重置
     * @param {*} val
     */
    reset(val) {
      this.$log.info('🐟🐟', val);
      this.choosePlanList = val;
      this.planList.forEach(item => {
        item.changeAllUnitsStatus(false);
      });
      this.changePlanList(this.getChooseIdList());
    }
  }
};
</script>
<style lang="less" scoped>
@import '@/assets/less/single/dialog.less';
@import '@/assets/less/single/componentization.less';

.choose-unit-header {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 20px;
  margin-bottom: 15px;
  justify-content: space-between;
  :deep(.el-input__inner) {
    height: 36px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #dcdfe6;
  }
  .choose-unit-header-plan {
    width: 314px;
  }
  .choose-unit-header-group {
    width: 200px;
  }
  :deep(.el-input__inner) {
    height: 36px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #dcdfe6;
  }
  :deep(.el-input__suffix-inner .el-icon-arrow-up) {
    transform: scale(0.36) rotateZ(180deg);
    transform-origin: center;
  }
  :deep(.el-select .el-input .el-select__caret.is-reverse) {
    transform: scale(0.36) rotate(0deg);
    transform-origin: center;
  }
  :deep(.el-input__suffix-inner .el-icon-arrow-up::before) {
    content: '\e7ae';
    font-family: 'iconfont';
    color: rgba(0, 0, 0, 0.65);
    height: 36px;
  }
}

.choose-unit-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  :deep(.el-button) {
    padding: 0 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    border-radius: 8px;
    width: auto;
  }
}
.choose-unit-main {
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  background: #f8f8f8;
  height: calc(100vh - 400px);
  overflow: auto;
  display: flex;
  flex-direction: column;
  .select-group {
    display: flex;
    align-items: center;
    margin: 10px;
    margin-bottom: 10px;
    padding-left: 20px;
    height: 50px;
    border-radius: 10px;
    background: #ededed;
    font-weight: bold;

    user-select: none;
    flex: 0 0 50px;
    & .level_1 {
      flex: 0 0 110px;
    }
    & .level_2 {
      flex: 0 0.6 200px;
    }

    & .level_3 {
      flex: 1 1 250px;
    }
  }
  .select-content {
    overflow-y: auto;
    padding: 0 10px;
    flex: 1;
    .unit-item[level='0'] {
      background-color: #eef0fa;
      flex: 1 1 130px;
    }

    .unit-children {
      flex: 1 1 calc(100% - 130px);
    }

    .unit-item[level='0'],
    .unit-item[level='1'] {
      .unit-item-desc-name[checked] {
        color: rgba(0, 0, 0, 0.65) !important;
      }
    }
    .unit-item[level='2'],
    .unit[lastLevel][isSpecialChapter] {
      .unit-item-desc-name[checked] {
        font-weight: bold !important;
      }
    }

    .bookNodeName {
      .ellipsis;
      width: 0;
    }
  }
  .select-content-empty {
    overflow-y: auto;
    padding: 0 10px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
