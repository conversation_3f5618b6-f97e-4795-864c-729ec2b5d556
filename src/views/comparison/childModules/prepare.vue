<template>
  <section class="comparison-prepare">
    <CProTable
      title="集备资源"
      :columns="currentColumns"
      ref="proTable"
      :initTable="unitList"
      :emptyPage="{ tip: '还没有集备资源包哦，快挑选备课包吧～', type: '5' }"
      @row-click="onPreview"
      :isHiddenPage="true"
    >
      <template slot="titleRight" v-if="!disabled">
        <div class="right-operation">
          <el-button
            class="right-operation-btn"
            @click="onChoosePrepareUnit"
            :disabled="!teachGroupId"
          >
            挑选备课包
          </el-button>
        </div>
      </template>
      <template slot="masterMember" scope="{row}">
        <div>{{ row.masterMembers[0].userName }}</div>
      </template>
      <template slot="empty">
        <el-button
          @click="onChoosePrepareUnit"
          type="primary"
          v-if="!disabled"
          :disabled="!teachGroupId"
          >挑选备课包</el-button
        >
      </template>
    </CProTable>
  </section>
</template>
<script>
import CProTable from '@/components/common/CProTable';
import { REPREPARE_MENU } from '@/object/prepare/prepare-enum';
export default {
  components: {
    CProTable
  },
  props: {
    unitList: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    teachGroupId: {
      type: String,
      default: ''
    }
  },
  computed: {
    currentColumns() {
      let width = '100px';
      const btns = [
        {
          label: '预览备课包',
          click: this.onPreview
        }
      ];
      if (!this.disabled) {
        btns.push({
          label: '移除',
          click: this.onDelete
        });
        width = '140px';
      }
      return [
        {
          label: '备课组',
          prop: 'prepareGroupName',
          showOverflowTooltip: true,
          minWidth: 200
        },
        {
          label: '教学内容名称',
          prop: 'preparationName',
          showOverflowTooltip: true,
          minWidth: 300,
          render: row => {
            return (
              <div class="preparation-name">
                <img
                  src={require('@/assets/images/comparison/level1-fire.png')}
                  class="fire-img"
                  show={row.isQuality}
                />
                <span>{row.preparationName}</span>
              </div>
            );
          }
        },
        {
          label: '主备人',
          prop: 'masterMember',
          slotName: 'masterMember',
          showOverflowTooltip: true,
          minWidth: 150
        },
        {
          label: '操作',
          prop: 'actions',
          slotName: 'actions',
          width,
          btns
        }
      ];
    }
  },
  data() {
    return {
      formColumns: []
    };
  },
  methods: {
    onChoosePrepareUnit() {
      this.$emit('onChoosePrepareUnit');
    },
    async onDelete(data, index) {
      await this.$confirm('是否确认移除该备课包？', '提示', {
        confirmButtonClass: 'confirm-btn',
        customClass: 'del-confirm-box'
      });
      this.$emit('onDelete', data, index);
    },
    onPreview(content) {
      const { id, teachPlanId, prepareGroupId } = content;
      let routerInfo = this.$router.resolve({
        path: '/prepare/plan/info',
        query: {
          id: id,
          prepareGroupId: prepareGroupId,
          planId: teachPlanId,
          prepareStatus: REPREPARE_MENU.PREPARE_LESSONS.key
        }
      });
      window.open(routerInfo.href, '_blank');
    }
  }
};
</script>

<style lang="less" scoped>
.comparison-prepare {
  :deep(.common-table) {
    padding: 0 10px 10px 10px;
    background: #fff;
    border: none;
    .el-table {
      background: #fff;
    }
  }
  :deep(td.el-table__cell) {
    .cell {
      background: rgba(248, 248, 248, 0.6) !important;
    }
    &::before {
      background: rgba(248, 248, 248, 0.6) !important;
    }
  }
  :deep(.el-button) {
    padding: 0 15px;
    height: 36px;
    box-sizing: border-box;
    border-radius: 8px;
    cursor: pointer;
  }
  :deep(.is-disabled) {
    cursor: not-allowed;
  }
  :deep(.box) {
    line-height: normal;
    .el-button {
      margin-top: 26px;
    }
  }
  .preparation-name {
    .ellipsis;
    width: 100%;
    position: absolute;
    margin-left: -20px;

    .fire-img {
      width: 14px;
      margin-right: 6px;
      position: relative;
      top: 2px;
      opacity: 0;

      &[show='true'] {
        opacity: 1;
      }
    }
  }
}
</style>
