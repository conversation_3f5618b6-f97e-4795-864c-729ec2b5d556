<template>
  <div class="comparison-basis">
    <div class="comparison-basis-item">
      <p class="comparison-basis-item-title">学校名称</p>
      <div class="comparison-basis-item-select">
        <p>
          <el-tooltip
            class="item"
            effect="dark"
            :content="userInfo.schoolName"
            placement="bottom"
            popper-class="table-form-tree-multi_tooltip"
          >
            <span>{{ userInfo.schoolName }}</span>
          </el-tooltip>
        </p>
      </div>
    </div>
    <div class="comparison-basis-item">
      <p class="comparison-basis-item-title">校区名称</p>
      <div class="comparison-basis-item-select">
        <p>
          <el-tooltip
            class="item"
            effect="dark"
            :content="campusName"
            placement="bottom"
            popper-class="table-form-tree-multi_tooltip"
          >
            <p>{{ campusName }}</p>
          </el-tooltip>
        </p>
      </div>
    </div>
    <div class="comparison-basis-item">
      <p class="comparison-basis-item-title">教研组</p>
      <div class="comparison-basis-item-select">
        <p>
          <el-select
            v-model="teachGroupId"
            placeholder="请选择"
            no-data-text="该校区下没有教研组"
            popper-class="hl-selectgroup-max-height-360 select-scroll-bar select-list-item"
          >
            <el-option
              v-for="item in groupList"
              :key="item.groupId"
              :label="item.groupName"
              :value="item.groupId"
            >
            </el-option>
          </el-select>
        </p>
      </div>
    </div>
    <div class="comparison-basis-item">
      <p class="comparison-basis-item-title">组长姓名</p>
      <div class="comparison-basis-item-select">
        <p>
          <el-tooltip
            class="item"
            effect="dark"
            :content="leaderName"
            placement="bottom"
            popper-class="table-form-tree-multi_tooltip"
          >
            <span>{{ leaderName }}</span>
          </el-tooltip>
        </p>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import { getTeachGroupList } from '@/api/comparison/index';
export default {
  computed: {
    // 展示当前的学校名+校区名
    // 无校区直接展示学校名
    campusName() {
      this.$log.info('🔥', this.campusList);
      let len = this.campusList.length;
      if (len === 1) {
        return this.userInfo.schoolName;
      }
      let campus = this.campusList.find(item => item.campusId == this.campusId);
      let name = this.userInfo.schoolName;
      if (campus?.campusName) {
        name = `${campus?.campusName || this.userInfo.schoolName}`;
      }
      return name;
    },
    ...mapState({
      userInfo: state => state.auth.userInfo,
      campusId: state => state.campus.campusId,
      campusList: state => state.campus.campusList
    })
  },
  props: {
    value: {
      type: String
    }
  },

  watch: {
    value(val) {
      this.teachGroupId = val;
    },

    teachGroupId(val) {
      this.$emit('input', val);
      this.$log.info('🔥🔥🔥', val);
      if (val) {
        this.leaderName = this.groupList.find(
          item => item.groupId === val
        ).leaderName;
      }
    }
  },
  data() {
    return {
      teachGroupId: '',
      leaderName: '',
      groupList: []
    };
  },
  mounted() {
    this.ajaxGetTeachGroupList();
  },
  methods: {
    /**
     * 获取教研组列表
     */
    async ajaxGetTeachGroupList() {
      try {
        const { data } = await getTeachGroupList({
          allowAdmin: true,
          groupType: 'prepare',
          tenantId: this.userInfo.tenantId
        });
        this.groupList = data.groupManageList;
        this.teachGroupId = this.groupList[0]?.groupId;
      } catch (error) {
        this.$log.warn('comparison teachgroup: ', error);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.comparison-basis {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-column-gap: 20px;
  .comparison-basis-item {
    flex: 1;
    font-weight: 500;
    font-size: 16px;
    color: #000000;
    line-height: 22px;
    overflow: hidden;
    .comparison-basis-item-title {
      margin-bottom: 10px;
    }
  }
  .comparison-basis-item-select {
    position: relative;
    height: 52px;
    margin-bottom: 10px;
    border-radius: 10px;
    cursor: pointer;

    & > p {
      height: 52px;
      width: calc(100% - 4px);
      margin-left: 2px;
      background: #eef9ff;
      border-radius: 10px;
      position: relative;
      z-index: 2;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      box-sizing: border-box;
      display: flex;
      align-items: center;
      line-height: 52px;
    }
    :deep(.el-tooltip) {
      display: inline-block;
      padding-right: 10px;
      box-sizing: border-box;
      margin-left: 20px;
      .ellipsis;
    }
    :deep(.el-select) {
      width: 100%;
    }
    :deep(.el-input__inner) {
      padding-left: 20px;
      background: transparent;
      width: 100%;
      border: none;
    }
    :deep(.el-input__inner) {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85) !important;
      font-weight: 500;
    }
    // select右侧箭头位置
    :deep(.el-input .el-input__suffix) {
      right: 8px !important;
    }
    :deep(.el-input__suffix-inner .el-icon-arrow-up) {
      transform: scale(0.4) rotateZ(180deg);
    }
    :deep(.el-input__suffix-inner .el-icon-arrow-up::before) {
      content: '\e7ae';
      font-family: 'iconfont';
      color: rgba(0, 0, 0, 0.65);
      // transform: scale(0.5) rotate(180deg);
    }
    &::after {
      content: '';
      width: calc(100%);
      height: calc(100% + 4px);
      position: absolute;
      border-radius: 11px;
      background: linear-gradient(
        316deg,
        rgba(23, 89, 238, 1),
        rgba(0, 191, 255, 1)
      );
      top: -2px;
      left: 0px;
      z-index: 1;
    }
  }
}
</style>
<style lang="less">
.select-list-item {
  .el-select-dropdown__list {
    padding: 11px 0;
    .el-select-dropdown__item {
      padding: 0 10px !important;
      margin: 0 10px;
      border-radius: 5px;
      font-weight: 700;
      &.hover {
        color: rgba(0, 0, 0, 0.65);
        background: rgba(@theme-color, 0);
      }
      &:hover {
        color: @theme-color !important;
        background: rgba(@theme-color, 0.1) !important;
      }
      &:focus {
        color: @theme-color !important;
        background: rgba(@theme-color, 0.1);
      }
    }
  }
}
</style>
