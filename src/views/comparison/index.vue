<template>
  <div class="fixed-page">
    <div class="comparison">
      <div
        class="tool-layout"
        v-if="!activityInfo"
        v-loading="!activityInfo"
      ></div>
      <!-- 顶部活动占为图 start-->
      <section class="comparison-title">
        <img
          asyn
          src="https://hl-common-prod.obs.cn-east-3.myhuaweicloud.com/hrjy/banner/banner-details.png"
        />
      </section>
      <!-- 顶部活动占为图 end-->

      <!-- 成果提交说明 start -->
      <section class="comparison-section">
        <comparisonTitle
          :url="require('@/assets/images/comparison/title1.png')"
          alt="成果提交说明"
        ></comparisonTitle>
        <ul class="comparison-illustrate">
          <li v-for="(item, index) in illustrate" :key="index">{{ item }}</li>
        </ul>
      </section>
      <!-- 成果提交说明 end -->

      <!-- 教研组信息确认 start -->
      <section class="comparison-section">
        <comparisonTitle
          :url="require('@/assets/images/comparison/title2.png')"
          alt="教研组信息确认说明"
        ></comparisonTitle>
        <teachGroup v-model="teachGroupId"></teachGroup>
      </section>
      <!-- 成果提交说明 end -->

      <!-- 特色沉淀 start -->
      <section class="comparison-section" style="margin-top: 164px">
        <comparisonTitle
          :url="require('@/assets/images/comparison/title3.png')"
          alt="特色沉淀"
          :intro="
            `每个教研组需提交${teachAchievementLimitMin}～${teachAchievementLimitMax}个优质资源包，单个资源包最多支持1G`
          "
        ></comparisonTitle>
        <characteristic-precipitation
          v-if="teachAchievementLimitMax"
          ref="characteristicRef"
          :maxFileNum="teachAchievementLimitMax"
          :originData="originData"
          :teachGroupId="teachGroupId"
          :disabled="!isCanSave"
          @emitUpdateData="emitUpdateData"
        ></characteristic-precipitation>
      </section>
      <!-- 特色沉淀 end -->

      <!-- 资源 start -->
      <section class="comparison-section">
        <comparisonTitle
          :url="require('@/assets/images/comparison/title4.png')"
          alt="集备资源"
          :intro="
            isCanSave
              ? `每个教研组需提交${prepareResMin}～${prepareResMax}个集备资源包`
              : `共提交${choosedUnitPlan.length}个集备资源包`
          "
        ></comparisonTitle>
        <Prepare
          :unitList="choosedUnitPlan"
          :disabled="!isCanSave"
          :teachGroupId="teachGroupId"
          @onChoosePrepareUnit="isShowChooseUnit = true"
          @onDelete="onDeletePrepareUnit"
        ></Prepare>
      </section>
      <!-- 资源 end -->

      <!-- 集备资源弹窗 start -->
      <chooseUnit
        ref="chooseUnitRef"
        v-model="isShowChooseUnit"
        :activityInfo="activityInfo"
        :maxCount="prepareResMax"
        :teachGroupId="teachGroupId"
        @emitSetChooseContent="emitSetChooseContent"
        @emitSetChooseContentList="emitSetChooseContentList"
      ></chooseUnit>
      <!-- 集备资源弹窗 end -->

      <!-- 底部操作区 start -->
      <section class="btn">
        <el-button class="btn-back" @click="goBack">返回</el-button>
        <el-button
          class="btn-submit"
          @click="ajaxPostSubmitData(true)"
          :disabled="!isCanSave || !teachGroupId"
          >{{ isCanSave ? '确认提交' : '成果已提交，专家评审中…' }}</el-button
        >
      </section>
      <!-- 底部操作区 end -->

      <!-- loading提示区域 start -->
      <!-- v-if="dataLoading" -->
      <section class="dataLoading" v-if="isHasStored">
        <i v-if="!isGetSuccess" class="iconfont icon-time"></i>
        <i v-if="isGetSuccess" class="iconfont icon-selected"></i>
        {{ isGetSuccess ? '已保存' : '正在保存...' }}
      </section>
      <!-- loading提示区域 end -->
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import comparisonTitle from './components/title.vue';
import teachGroup from './childModules/teachGroup.vue';
import characteristicPrecipitation from './components/CharacteristicPrecipitation';
import {
  postAchievementShot,
  getActivityInfo,
  getAchievementApplication,
  postAchievementSubmit
} from '@/api/comparison/index';
import chooseUnit from './childModules/chooseUnit';
import { getToken } from '@/plugins/core/auth';
import { cloneDeep } from 'lodash';
import Prepare from './childModules/prepare.vue';
import Unit from '@/object/Unit.js';
export default {
  components: {
    Prepare,
    comparisonTitle,
    characteristicPrecipitation,
    teachGroup,
    chooseUnit
  },

  watch: {
    teachGroupId(val) {
      this.$log.info('teachGroupId:', val);
      this.isHasStored = false;
      // 查询当前教研组是否有历史暂存数据
      this.ajaxGetAchievementApplication();
      this.choosedUnitPlan = [];
      this.preparePlanDetailList = [];
    },
    isShowChooseUnit() {
      this.$log.info('🔥🔥🔥', cloneDeep(this.choosedUnitPlan));
      this.$refs.chooseUnitRef.reset(cloneDeep(this.choosedUnitPlan));
    }
  },
  data() {
    return {
      activityInfo: null,
      prepareResMin: 0, // 集备资源要求的最小个数
      prepareResMax: 0, // 集备资源要求的最大个数

      teachGroupId: '', // 教研组id
      // isShowChooseUnit: false, // 是否展示选择资源弹窗
      originData: [], // 初始数据
      preparePlanDetailList: [], // 教学计划明细列表
      summaryResourceList: [], // 成果沉淀列表
      choosedUnitPlan: [],
      isShowChooseUnit: false, // 是否展示选择资源弹窗
      dataLoading: false, // 数据暂存加载loading
      isGetSuccess: false,
      isCanSave: true,
      isHasStored: false, // 暂存过
      needSubmit: false // 异常处理 暂存后需要提交活动数据
    };
  },
  mounted() {
    this.ajaxGetActivityInfo();
  },
  computed: {
    ...mapState({
      campusId: state => state.campus.campusId
    }),
    // 说明
    illustrate() {
      return [
        `教研资源沉淀：沉淀"教-学-评"一体化课例资源包(教案、课件/视频)，每个教研组最多上报${this.teachAchievementLimitMax}个优质资源，每沉淀一个优质资源加2.5分，同类优质资源不累加加分，此项最高分为10分。`,
        `集备资源：线上集体备课过程中沉淀的优质备课包(教案+课件)，以学科组为单位，每个学科组最多上报${this.prepareResMax}个优质备课包，最少上报${this.prepareResMin}个优质课包，每沉淀一个优质资源包加1分，此项最高分为10分。`,
        '教研组提交成果后，将由学科专家进行评审，评审结果将通过海亮OA进行公布。'
      ];
    }
  },
  methods: {
    /**
     * 召集所有成果提交参数
     * needVerify 是否需要校验
     */
    getAllData(needVerify) {
      this.$log.info('needVerify:', needVerify);
      let list = this.summaryResourceList.filter(item => item.pubResourceUrl);
      list = list.map(item => {
        if (item.pubResourceUrl) {
          return {
            pubResourceName: item.pubResourceName,
            pubResourceFormat: item.pubResourceFormat,
            pubResourceUrl: item.pubResourceUrl,
            size: item.size
          };
        }
      });
      // 成果提交资源校验
      if (needVerify) {
        this.$log.info(list);
        if (!list.length) {
          this.$message.warning('特色沉淀为空，不能提交！');
          return;
        }
        let idList = this.preparePlanDetailList;
        if (!idList.length) {
          this.$message.warning('集备资源为空，不能提交！');
          return;
        }
        // if (idList.length < 2 || idList.length > 20) {
        if (
          idList.length < this.prepareResMin ||
          idList.length > this.prepareResMax
        ) {
          this.$message.warning(
            `集备资源包数量需要在${this.prepareResMin}-${this.prepareResMax}以内，否则无法提交！`
          );
          return;
        }
      }

      return {
        saasSchoolId: getToken('schoolId'),
        saasCampusId: this.campusId,
        teachGroupId: this.teachGroupId,
        activityId: this.activityInfo?.activityId,
        version: this.originData?.version || 0,
        // 集备资源ids
        preparePlanDetailIdList: this.preparePlanDetailList,
        // 特色沉淀列表
        summaryResourceList: list
      };
    },
    /**
     * 接收成果沉淀数据
     */
    emitUpdateData(list) {
      this.$log.info('我来暂存嘞 🐶 🐶 🐶');
      this.summaryResourceList = list;
      this.ajaxPostUploadData(); // 数据暂存到后台
    },
    /**
     * 暂存数据
     * @param {Boolean} needSubmit 是否需要再次提交
     */
    async ajaxPostUploadData() {
      if (!this.isCanSave) {
        return;
      }
      this.isGetSuccess = false;
      // this.dataLoading = true;
      const params = this.getAllData(); // 获取到所有的参数
      if (!params) return;
      this.isHasStored = true;
      try {
        const { data } = await postAchievementShot(params);
        this.$log.info('暂存成功嘞 🐶', data);
        if (data && data.snapshot) {
          this.originData = data.snapshot; // 暂存后更新数据
          // 备课包缺少资源时异常处理，需要直接继续提交活动数据
          if (this.needSubmit) {
            this.$log.info(
              '备课包缺少资源时异常处理，需要直接继续提交活动数据🐶',
              data
            );
            this.ajaxPostSubmitData(false);
            this.needSubmit = false;
          }
        }
        setTimeout(() => {
          this.isGetSuccess = true;
        }, 1000);
      } catch (e) {
        this.$log.warn('ajax ajaxPostUploadData error', e);
      } finally {
        // setTimeout(() => {
        //   this.dataLoading = false;
        // }, 2000);
      }
    },
    /**
     * 提交成果数据
     * @param {Boolean} isNeedTwiceConfirm 是否需要二次确认
     */
    async ajaxPostSubmitData(isNeedTwiceConfirm = true) {
      if (!this.isCanSave) {
        return;
      }
      const params = this.getAllData('needVerify');
      if (!params) return;
      this.$log.info('🐶 我来提交成果数据嘞：', params, this.originData);

      if (isNeedTwiceConfirm) {
        await this.$confirm(
          '请确认是否提交成果，成果提交后不可修改，请谨慎思考后再提交哦~',
          '确认提交成果',
          {
            confirmButtonClass: 'confirm-btn'
          }
        );
      }

      try {
        this.needSubmit = false;
        this.isCanSave = false; // 更新为禁止编辑状态
        const { data } = await postAchievementSubmit(params);
        this.$log.info('data:', data);
      } catch (e) {
        this.isCanSave = true; // 更新为禁止编辑状态
        this.$log.warn('ajax ajaxPostUploadData error', e);
        if (e.data && e.data.preparePlanDetailIdList) {
          // 提交时有备课包不满足要求, 用户选择如何处理
          this.onChooseWhichType(e);
        } else if (e.status === 400 && !+e.data) {
          this.$message({
            message: e.message,
            type: 'warning'
          });
        }
      }
    },
    /**
     * 提交时备课包缺少资源时异常处理, 用户选择如何处理
     */
    onChooseWhichType(res) {
      this.$confirm(res.message, '提示', {
        confirmButtonText: '继续提交',
        cancelButtonText: '取消',
        confirmButtonClass: 'confirm-btn',
        type: 'warning'
      })
        .then(() => {
          // 备课包缺少资源时异常处理，需要先去暂存获取version 然后直接继续提交活动数据
          // 1. 移除不满足条件的备课包
          this.needSubmit = true;
          let delList = res.data.preparePlanDetailIdList;
          this.preparePlanDetailList = this.preparePlanDetailList.filter(
            item => delList.indexOf(item) === -1
          );
          this.choosedUnitPlan = this.choosedUnitPlan.filter(
            item => delList.indexOf(item.id) === -1
          );
          this.ajaxPostUploadData(); // 数据暂存到后台
          // this.onDeletePrepareUnit('', index);
        })
        .catch(() => {
          // 1. 关闭对话框
          // 2. 移除不满足条件的备课包,用户可以回去重新选
          let delList = res.data.preparePlanDetailIdList;
          this.preparePlanDetailList = this.preparePlanDetailList.filter(
            item => delList.indexOf(item) === -1
          );
          this.choosedUnitPlan = this.choosedUnitPlan.filter(
            item => delList.indexOf(item.id) === -1
          );
          this.ajaxPostUploadData(); // 数据暂存到后台
          // this.onDeletePrepareUnit('', index);
        });
    },
    onDeletePrepareUnit11(data, index) {
      this.choosedUnitPlan.splice(index, 1);
      this.preparePlanDetailList.splice(index, 1);
      this.ajaxPostUploadData(); // 数据暂存到后台
    },
    /**
     * 活动查询
     */
    async ajaxGetActivityInfo() {
      try {
        const { data } = await getActivityInfo();
        this.activityInfo = data;
        const {
          prepareAchievementLimit = [],
          teachAchievementLimit = []
        } = data;
        this.teachAchievementLimitMin = Number(teachAchievementLimit[0] || 0);
        this.teachAchievementLimitMax = Number(teachAchievementLimit[1] || 0);
        this.prepareResMin = Number(prepareAchievementLimit[0] || 0);
        this.prepareResMax = Number(prepareAchievementLimit[1] || 0);
      } catch (error) {
        this.$log.info('ajaxGetActivityInfo: ', error);
      }
    },
    /**
     * 查询是否有暂存数据-活动成果申报表查询
     */
    async ajaxGetAchievementApplication() {
      if (!this.teachGroupId) {
        // this.isCanSave = false;
        return;
      }
      try {
        const res = await getAchievementApplication({
          activityId: this.activityInfo?.activityId,
          teachGroupId: this.teachGroupId
        });
        this.$log.info('🐳 查询是否有暂存数据');
        this.originData = res.data || {};
        // 为了兼容有无暂存数据情况 无暂存无res.data
        this.isCanSave = !res.data || res.data.status !== 'finish';
        if (!res.data) {
          // 无暂存，需要清空当前页面上传资源
          this.$log.info('🐳无暂存');
          // 清空特色沉淀
          this.$refs?.characteristicRef.onClearFlieList();
        } else {
          this.$log.info('🐳有暂存', res);
          // 有暂存，要反显缓存的数据
          // 必须要先处理集备资源,后处理特色沉淀,否则暂存机制会有问题
          this.$log.info('🔥', res.data);
          // 集备资源反显数据处理特色沉淀
          if (res.data.preparePlanDetailList) {
            res.data.preparePlanDetailList.forEach(item => {
              this.preparePlanDetailList.push(item.preparePlanDetailId);
              this.choosedUnitPlan.push(
                new Unit(
                  {
                    id: item.preparePlanDetailId,
                    preparationName: item.displayPreparePlanDetailName,
                    prepareGroupName: item.prepareGroupName,
                    displayStatus: item.displayStatus,
                    prepareGroupId: item.prepareGroupId,
                    teachPlanId: item.teachPlanId,
                    masterMembers: [
                      {
                        userId: item.teacherId,
                        userName: item.teacherName
                      }
                    ],
                    level: item.level
                  },
                  1
                )
              );
            });
          }
          // 特色沉淀反显数据处理
          this.$refs?.characteristicRef.setFlieList(
            this.originData.summaryResourceList
          );
        }
      } catch (error) {
        this.$log.info('ajaxGetAchievementApplication: ', error);
      }
    },
    /**
     * 接收教学计划明细列表
     */
    emitSetChooseContent(idList) {
      this.$log.info('🌹', idList);
      this.preparePlanDetailList = idList;
      this.ajaxPostUploadData(); // 数据暂存到后台
    },
    emitSetChooseContentList(data) {
      this.$log.info('🌹', data);
      this.choosedUnitPlan = data;
    },
    onDeletePrepareUnit(data, index) {
      this.choosedUnitPlan.splice(index, 1);
      this.preparePlanDetailList.splice(index, 1);
      this.ajaxPostUploadData(); // 数据暂存到后台
    },
    goBack() {
      const { back } = this.$route.query;
      if (back) {
        location.href = decodeURIComponent(back);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.fixed-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
}
.comparison {
  background: url('~@/assets/images/comparison/bg1.png'),
    url('~@/assets/images/comparison/bg2.png'),
    url('~@/assets/images/comparison/bg3.png'),
    url('~@/assets/images/comparison/bg4.png'),
    url('~@/assets/images/comparison/bg5.png'),
    url('~@/assets/images/comparison/bg6.png'), #eef9ff;
  background-repeat: no-repeat;
  background-size: 220px auto, 104px auto, 82px auto, 65px auto, 252px auto,
    183px auto;
  background-position: left 31vw, right calc(31vw + 459px),
    left calc(31vw + 957px), right calc(31vw + 1317px), left bottom,
    right 132px bottom;
  min-width: 1080px;
  overflow-x: auto;
  .tool-layout {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vw;
    background: transparent;
  }
  .comparison-title {
    width: 100%;
    & > img {
      width: 100%;
    }
  }
  .comparison-section {
    margin: 30px 120px 100px;
  }
  .comparison-illustrate {
    padding: 20px;
    background: #ffffff;
    border-radius: 20px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 26px;
    text-align: left;
    & > li {
      list-style-type: none; /* 先去除小圆点 */
      background: url(~@/assets/images/comparison/star.png); /* 修改为背景图片 */
      background-size: 16px 16px; /* 设置背景图片100%填充 */
      background-repeat: no-repeat;
      background-position: 10px 5px;
      padding-left: 40px;
    }
    & > li:not(:last-child) {
      margin-bottom: 15px;
    }
  }
  .btn {
    display: flex;
    justify-content: center;
    margin: 100px 0;
    .el-button {
      min-width: 190px;
      padding: 0 52px;
      height: 62px;
      border-radius: 45px;
      border: 1px solid rgba(0, 0, 0, 0.25);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.45);
    }
    &-back {
      background: transparent;
      &:hover {
        color: #5c6bd0;
        border-color: #5c6bd0 !important;
        background: transparent;
      }
    }
    &-submit {
      margin-left: 20px;
      background: #5c6bd0;
      border-color: #5c6bd0 !important;
      color: #fff !important;
      &:hover {
        background: #7d89d9 !important;
        border-color: #7d89d9 !important;
      }
      &[disabled='disabled'] {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }
  }
  .dataLoading {
    position: fixed;
    bottom: 10px;
    right: 10px;
    padding: 10px;
    display: flex;
    align-items: center;
    border-radius: 10px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 13px;
    line-height: 16px;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    .iconfont {
      margin-right: 5px;
    }
  }
}
@media screen and (max-width: 1080px) {
  .comparison {
    background-position: left 345px, right calc(345px + 459px),
      left calc(345px + 957px), right calc(345px + 1317px), left bottom,
      right 132px bottom;
  }
}
</style>
