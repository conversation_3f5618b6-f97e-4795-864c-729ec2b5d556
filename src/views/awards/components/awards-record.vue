<template>
  <el-dialog
    title="审核记录"
    :visible.sync="visible"
    width="420px"
    custom-class="award-record"
    :before-close="handleClose"
  >
    <div class="preview-box">
      <div v-for="item in dataList" :key="item.id" class="li">
        <div class="content-top">
          <div class="status">
            <img
              :src="statusImgMap[item.auditStatus].src"
              alt=""
              srcset=""
              class="ico"
            />
            <!--  :style="{ color: statusImgMap[item.auditStatus].color }" -->
            <div class="text">
              <div class="tit ellipsis">
                {{ statusImgMap[item.auditStatus].writer }}
              </div>
              <div v-if="auditAuth && item.displayIntervalTime">
                (已审核{{ item.displayIntervalTime }})
              </div>
              <div v-else>{{ item.gmtCreated }}</div>
            </div>
          </div>
          <div class="user">
            <img
              v-if="item.creatorAvatar"
              :src="item.creatorAvatar"
              class="avatar"
            />
            <CAvatar
              v-else
              class="avatar"
              :name="item.creatorName"
              :currentBgc="item.creatorAvatarColor"
            ></CAvatar>
            <div class="tit">
              <el-tooltip
                class="item"
                effect="dark"
                :content="item.creatorName"
                placement="top"
                :disabled="item.creatorName.length < 5"
              >
                <span
                  >{{ item.creatorName.slice(0, 4)
                  }}<span v-if="item.creatorName.length >= 5">...</span>
                </span>
              </el-tooltip>
            </div>
            <!-- <div class="text">
            <div class="tit">{{ item.reviewerPeopleName }}</div>
            <div>
              {{ item.reviewStatusRecordName
              }}{{
                item.reviewStatusRecord == '-1' ? '原因：' + item.reason : ''
              }}
            </div>
          </div> -->
          </div>
        </div>
        <div
          v-if="item.auditStatus === AuditStatusMap.notPass.key"
          class="content-bottom"
        >
          驳回理由： {{ item.reason }}
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getAwardAuditRecord } from '@/api/awards';
import { AuditStatusMap } from '../utils/enum';
import CAvatar from '@/components/common/CAvatar';
export default {
  name: 'AwardsRecord',
  components: {
    CAvatar
  },
  props: {
    // 待审核权限
    auditAuth: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      AuditStatusMap,
      visible: false,
      dataList: [],
      // 背景图标枚举
      statusImgMap: {
        unaudited: {
          color: '#333B4D',
          writer: '提交审核',
          src: require('@/assets/images/awards/record-submit.png')
        },
        inAudited: {
          color: '#333B4D',
          writer: '审核中',
          src: require('@/assets/images/awards/record-audit.png')
        },
        resubmit: {
          color: '#333B4D',
          writer: '重新提交',
          src: require('@/assets/images/awards/record-submit.png')
        },
        pass: {
          color: '#389E61',
          writer: '通过',
          src: require('@/assets/images/awards/record-success.png')
        },
        notPass: {
          color: '#F04444',
          writer: '驳回',
          src: require('@/assets/images/awards/record-failed.png')
        }
      }
      // 审核状态颜色枚举
      // statusColortMap: {
      //   unaudited: '#333B4D',
      //   pass: '#389E61',
      //   notPass: '#F04444'
      // }
    };
  },
  methods: {
    show(obj) {
      this.visible = true;
      this.getDataList(obj);
    },
    handleClose() {
      this.visible = false;
    },
    // 获得审核记录
    async getDataList(obj) {
      let { prizeId } = obj;
      let res = await getAwardAuditRecord({
        awardApplyId: prizeId
      });
      this.dataList = res.data.awardAuditRecordList;
    }
  }
};
</script>

<style lang="less" scoped>
.preview-box {
  // padding: 20px;
  // border: 1px solid #dfdfdf;
  // border-radius: 20px;
  // background: #fff;
  max-height: calc(100vh - 100px - 54px);
  padding: 5px 25px 20px 14px;
  .li {
    position: relative;
    margin-bottom: 30px;
    .content-top {
      display: flex;
      align-items: stretch;
      .status {
        // padding-bottom: 30px;
        margin-right: 30px;
        display: flex;
        height: inherit;
        font-size: 12px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.55);
        position: relative;
        .ico {
          // margin-right: 5px;
          width: 40px;
          height: 40px;
          // border-radius: 8px;
        }
        .text {
          padding-left: 5px;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.45);
          font-size: 12px;
          .tit {
            margin-bottom: 3px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.65);
            font-size: 14px;
          }
        }
      }
      .user {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        flex: 1;
        // border-bottom: 1px solid #dfdfdf;
        font-size: 12px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.55);
        // padding-bottom: 10px;
        .avatar {
          flex-shrink: 0;
          margin-right: 7px;
          width: 25px;
          height: 25px;
          border-radius: 50%;
          margin-top: 2px;
        }
        .tit {
          // margin-bottom: 8px;
          font-weight: bold;
          color: rgba(0, 0, 0, 0.65);
          font-size: 14px;
        }
      }
    }
    .content-bottom {
      padding-left: 45px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 20px;
      font-size: 13px;
      margin-top: 3px;
    }
    &:nth-child(2n) {
      .ico {
      }
    }
    &:last-child {
      margin-bottom: 0;
      .status {
        padding-bottom: 0;
        &::after {
          display: none;
        }
      }
      .user {
        padding-bottom: 0;
        // padding-top: 2.5px;
        border-bottom: none;
      }
    }
    &::after {
      content: '';
      width: 2px;
      height: calc(100% - 10px);
      background: #c2c2c2;
      position: absolute;
      left: 19px;
      top: 40px;
    }
    &:last-of-type::after {
      display: none;
    }
  }
}
/deep/ .award-record {
  .el-dialog__header {
    text-align: left;
    border-bottom: unset;
  }
  .el-dialog__body {
    background: #ffffff;
    padding: 0 !important;
  }
}
</style>
