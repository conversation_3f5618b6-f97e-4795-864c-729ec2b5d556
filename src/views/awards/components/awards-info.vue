<!-- 奖项详情 -->
<template>
  <div>
    <!-- 头部 -->
    <div class="page-title" v-loading="pageLoading">
      <i
        class="back-icon iconfont icon-icon-common-arrow-left"
        @click="onGoBack"
      ></i>
      <div class="txt" v-title data-title="奖项详情">
        奖项详情
      </div>
      <div
        class="award-status"
        :style="{
          background: auditStatusMapDesc[formData.auditStatus].bgcColor,
          color: auditStatusMapDesc[formData.auditStatus].color
        }"
        v-if="auditStatusMapDesc[formData.auditStatus]"
      >
        <div
          class="circle"
          :style="{
            background: auditStatusMapDesc[formData.auditStatus].color
          }"
        ></div>
        {{ auditStatusMapDesc[formData.auditStatus].desc }}
      </div>
      <div class="btn-box">
        <el-button size="medium" @click="goInfoHandle">查看审核记录</el-button>
        <!-- 只有申请人，审核失败时展示修改按钮 -->
        <el-button
          type="primary"
          size="medium"
          @click="editDataHandle"
          v-if="
            type === 1 && formData.auditStatus === AuditStatusMap.notPass.key
          "
          >重新发起</el-button
        >
        <!-- 审核人操作按钮，只有审核人身份，奖项待审核才展示 -->
        <div v-if="auditAuth">
          <el-button
            type="danger"
            size="medium"
            class="reject"
            @click.stop="postAuditHandle(1)"
            :loading="submitLoading"
            v-auth="['group:awards-audit:awards-info:audit']"
            >驳回</el-button
          >
          <el-button
            type="primary"
            size="medium"
            @click.stop="postAuditHandle(2)"
            :loading="submitLoading"
            v-auth="['group:awards-audit:awards-info:audit']"
            >通过</el-button
          >
        </div>
      </div>
    </div>
    <!-- 详情 -->
    <div v-if="formData.awardType" class="form-info-wrap">
      <div class="info-wrap-item info-wrap-right">
        <img
          class="audit-status-bg"
          :src="auditStatusMap[formData.auditStatus]"
          alt=""
          srcset=""
        />
        <course-card title="基础信息" :afterClass="true">
          <div class="form-info">
            <div
              class="form-item"
              :class="{
                'title-paper':
                  formData.awardType !== AwardTypeMap.paper.key &&
                  formData.awardType !== AwardTypeMap.research.key &&
                  formData.awardType !== AwardTypeMap.teach.key
              }"
            >
              <div class="label">奖项类型</div>
              <div class="value ellipsis">
                {{ formData.displayAwardType }}
              </div>
            </div>
            <!-- 论文著作时展示 -->
            <div
              class="form-item"
              v-if="
                formData.awardType === AwardTypeMap.paper.key ||
                  formData.awardType === AwardTypeMap.research.key
              "
            >
              <div class="label">
                <span v-if="formData.awardType === AwardTypeMap.paper.key"
                  >第一作者</span
                >
                <span v-else>课题负责人</span>
              </div>
              <div class="value ellipsis">
                {{ formData.isFirstAuthor ? '是' : '否' }}
              </div>
            </div>
            <!-- 课题研究时展示 -->
            <div class="form-item" v-if="formData.awardsTypeCode === 1">
              <div class="label">课题负责人</div>
              <div class="value">
                {{ formData.firstAuthorPrincipal ? '是' : '否' }}
              </div>
            </div>
            <!-- 教学活动时展示 -->
            <div
              class="form-item"
              v-if="formData.awardType === AwardTypeMap.teach.key"
            >
              <div class="label">活动类型</div>
              <div class="value ellipsis">
                {{ formData.displayAwardSubType }}
              </div>
            </div>
            <div class="form-item title-paper">
              <div class="label">
                {{ AwardTypeMap[formData.awardType].awardName }}
              </div>
              <div class="value ellipsis">{{ formData.awardName }}</div>
            </div>
            <div class="form-item">
              <div class="label">获奖日期</div>
              <div class="value">
                {{ formData.awardDate && formData.awardDate.split(' ')[0] }}
              </div>
            </div>
            <div class="form-item">
              <div class="label">获奖级别</div>
              <div class="value ellipsis">
                {{ formData.displayAwardLevel }}
              </div>
            </div>
            <div class="form-item">
              <div class="label">获奖名次</div>
              <div class="value ellipsis">
                {{ formData.displayAwardRank }}
              </div>
            </div>
            <div class="form-item">
              <div class="label">奖项学科</div>
              <div class="value ellipsis">
                {{ formData.subjectName }}
              </div>
            </div>
            <div class="form-item title-paper" v-if="formData.comment">
              <div class="label">备注</div>
              <div class="value">
                {{ formData.comment }}
              </div>
            </div>
            <!-- <div class="form-item">
              <div class="label">获奖文件</div>
              <div class="value">
                <b-file-list
                  class="file-item-list"
                  :fileList="formData.evidentialResourceList"
                  :canEdit="true"
                  v-if="formData.evidentialResourceList && formData.evidentialResourceList.length"
                ></b-file-list>
                <span v-else>-</span>
              </div>
            </div> -->
          </div>
        </course-card>
      </div>
      <div class="info-wrap-item info-wrap-left">
        <course-card
          title="获奖证明"
          :afterClass="true"
          class="award-certificate"
        >
          <b-file-list
            class="file-item-list"
            :fileList="formData.evidentialResourceList"
            :canEdit="true"
            v-if="
              formData.evidentialResourceList &&
                formData.evidentialResourceList.length
            "
          ></b-file-list>
          <span v-else>-</span>
        </course-card>
        <course-card
          title="获奖成果"
          :afterClass="true"
          class="award-certificate"
        >
          <b-file-list
            class="file-item-list"
            :fileList="formData.achievementResourceList"
            :canEdit="true"
            v-if="
              formData.achievementResourceList &&
                formData.achievementResourceList.length
            "
          ></b-file-list>
          <EmptyPage v-else tip="教师未上传获奖成果" type="5" />
        </course-card>
      </div>
    </div>
    <!-- 审核记录 -->
    <AwardsRecord ref="refDialog" :auditAuth="auditAuth" />
    <!-- 驳回理由 -->
    <AwardsReject ref="refRejectDialog" />
  </div>
</template>

<script>
import BFileList from '@/components/business/BFileList';
import AwardsRecord from './awards-record';
import AwardsReject from './awards-reject';
import {
  getAwardAuditInfo,
  getAwardRequestInfo,
  postAwardAudit
} from '@/api/awards';
import { converterResource } from '@/components/business/BFileList/util';
import CourseCard from '@/components/common/CourseNameCard';
import EmptyPage from '@/components/business/EmptyPage';
import {
  AuditStatusMap,
  AwardLevelMap,
  AwardRankMap,
  AwardSubTypeMap,
  AwardTypeMap
} from '../utils/enum';
import { mapState } from 'vuex';

export default {
  name: 'AwardsDataInfo',
  components: {
    BFileList,
    AwardsRecord,
    AwardsReject,
    CourseCard,
    EmptyPage
  },
  props: {
    type: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      AwardTypeMap,
      AwardSubTypeMap,
      AwardRankMap,
      AwardLevelMap,
      AuditStatusMap,
      pageLoading: true,
      formData: {},
      // 审核状态的对应图标
      auditStatusMap: {
        unaudited: require('@/assets/images/awards/award-audit-ing.png'),
        pass: require('@/assets/images/awards/award-success.png'),
        notPass: require('@/assets/images/awards/award-failed.png')
      },
      // 奖项名称集合
      awardsNameMap: {
        0: '论文名称',
        1: '课题名称',
        2: '活动名称',
        3: '奖项名称'
      },
      // 提交禁用，防止重复点击
      submitLoading: false,
      pageType: ''
    };
  },
  watch: {
    formData: {
      deep: true,
      handler(obj) {
        // 当为审核人，且为待审核状态，则改变图标
        if (
          this.type === 2 &&
          obj.auditStatus === AuditStatusMap.unaudited.key
        ) {
          this.auditStatusMap[0] = require('@/assets/images/awards/award-audit-ing.png');
        }
      }
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.auth.userInfo
    }),
    id() {
      let { id } = this.$route.query;
      return id;
    },
    // 审核状态的对应描述
    auditAuth() {
      // 审核人操作按钮，只有审核人身份，奖项待审核才展示
      let status = this.formData.auditStatus === AuditStatusMap.unaudited.key;
      let selfAuth = this.userInfo.id === this.formData.auditorId;
      return this.type === 2 && status && selfAuth;
    },
    auditStatusMapDesc() {
      return {
        unaudited: {
          desc: `${
            this.type === 2 && this.userInfo.id === this.formData.auditorId
              ? '待审核'
              : '审核中'
          }`,
          color: 'rgba(224, 162, 52, 1)',
          bgcColor: 'rgba(224, 162, 52, 0.1)'
        },
        pass: {
          desc: '已通过',
          color: 'rgba(34, 148, 83, 1)',
          bgcColor: 'rgba(34, 148, 83, 0.1)'
        },
        notPass: {
          desc: '未通过',
          color: 'rgba(243, 71, 24, 1)',
          bgcColor: 'rgba(243, 71, 24, 0.1)'
        }
      };
    }
  },
  created() {
    this.pageType = this.$route.query.pageType;
  },
  mounted() {
    this.getDataInfo();
  },
  methods: {
    // 回到奖项申请列表
    onGoBack() {
      if (document.length > 1) {
        this.$router.go(-1);
      } else {
        if (this.type === 2) {
          this.$router.push('/activity-manage/awards-audit');
        } else {
          this.$router.push('/personal-space/request-awards');
        }
        if (this.$route.query?.isHomeTo) {
          this.$router.push('/myHome');
        }
      }
    },
    // 查询奖项详情
    async getDataInfo() {
      let res = {};
      // 奖项审核（2）和奖项申请，调用接口不同
      if (this.type === 2) {
        res = await getAwardAuditInfo({
          awardApplyId: this.id
        });
      } else {
        res = await getAwardRequestInfo({
          awardApplyId: this.id
        });
      }
      this.formData = res.data;
      // 解析文件参数，重新组合
      this.formData.evidentialResourceList = converterResource(
        res.data.evidentialResourceList
      );
      this.formData.achievementResourceList = converterResource(
        res.data.achievementResourceList
      );
      this.pageLoading = false;
    },
    // 查看审核记录
    goInfoHandle() {
      this.$refs.refDialog.show({
        prizeId: this.formData.awardApplyId || this.formData.prizeId
      });
    },
    // 修改奖项申请
    editDataHandle() {
      this.$router.push({
        path: '/personal-space/request-awards/create',
        query: {
          id: this.id
        }
      });
    },
    // 审核通过/驳回
    postAuditHandle(type) {
      // 驳回
      if (type === 1) {
        this.$refs.refRejectDialog.show();
      } else {
        // 通过
        this.postDataHandle();
      }
    },
    // 同意申请
    async postDataHandle() {
      this.submitLoading = true;
      let { id } = this.$route.query;
      let params = {
        awardApplyId: id,
        auditStatus: AuditStatusMap.pass.key,
        source: 'pc'
      };
      try {
        const res = await postAwardAudit(params);
        if (res.success) {
          this.$message.success('审核状态更新成功！');
          this.$router.push({
            path: '/activity-manage/awards-audit',
            query: {
              refresh: true
            }
          });
        }
      } catch (e) {
        this.$log.warn('postAwardAudit', e);
      } finally {
        this.submitLoading = false;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.page-title {
  position: relative;
  display: flex;
  align-items: center;
  height: 28px;
  line-height: 28px;
  font-size: 20px;
  font-weight: bold;
  color: @mainTxtColor;
  .back-icon {
    // margin-left: 5px;
    margin-right: 10px;
    width: 25px;
    height: 25px;
    line-height: 25px;
    font-size: 12px;
    text-align: center;
    border-radius: 5px;
    cursor: pointer;
    background-color: @iconColor;
    color: rgba(0, 0, 0, 0.65);
    &:hover {
      background-color: @iconHoverColor;
    }
  }
  .btn-box {
    display: flex;
    justify-content: flex-end;
    flex: 1;
    text-align: right;

    .el-button {
      border-radius: 8px;
    }
    .reject {
      margin-left: 10px;
    }
  }
  .award-status {
    width: 68px;
    height: 23px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 14px;
    margin-left: 10px;
    font-size: 12px;
    font-family: CangerYuYang-W04;
    font-weight: normal;
    .circle {
      width: 7px;
      height: 7px;
      border-radius: 50%;
      margin-right: 4px;
    }
  }
}
.form-info-wrap {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-top: 20px;
  .info-wrap-item {
    width: 50%;
  }
  .info-wrap-right {
    position: relative;
    .audit-status-bg {
      position: absolute;
      right: 0px;
      top: -20px;
      width: 148px;
      height: 100px;
      z-index: 1;
    }
  }
  .info-wrap-left {
    .award-certificate {
      margin-bottom: 20px;
      .file-item-list {
        margin-top: 16px;
      }
    }
  }

  // position: relative;
  // top: 20px;
  // padding: 10px 20px 20px;
  // display: flex;
  // flex-direction: column;
  // align-items: center;
  // background-color: @menuBg;
  // border: 1px solid @menuBorderColor;
  // border-radius: 10px;
  .form-info {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 10px;
    padding-top: 16px;
    .form-item {
      width: calc(50% - 5px);
      background: #fafafa;
      border-radius: 10px;
      padding: 15px;
      box-sizing: border-box;
      // position: relative;
      // padding: 20px 0;
      // display: flex;
      // gap: 10px;
      // align-items: flex-start;
      // &:not(:last-child):before {
      //   content: '';
      //   position: absolute;
      //   left: 110px;
      //   right: 0;
      //   bottom: 0;
      //   height: 1px;
      //   background-color: @menuBorderColor;
      // }
      .label {
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        color: @menuSubTxtColor;
        margin-bottom: 8px;
      }
      .value {
        flex: 1;
        position: relative;
        line-height: 20px;
        font-size: 14px;
        color: @popSubTxtColor;
        word-break: break-all;
        /deep/ img {
          display: inline-block;
          max-width: 100%;
        }
        &.mutil {
          white-space: normal;
        }
        .ellipsis {
          .ellipsis();
        }
      }
    }
    .title-paper {
      width: 100%;
    }
  }
}
</style>
