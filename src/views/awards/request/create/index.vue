<template>
  <div class="page-box" v-loading="pageLoading">
    <!-- 顶部操作栏 -->
    <div class="nav-btn-wrap">
      <CSelect
        ref="cSelect"
        :list="teachGroupLeaders"
        :initValue="assessorItem"
        @change="setSelectTargetItem"
        v-if="!id || formData.auditorGroupDeleted"
      >
        <div v-if="assessorItem" class="approver">
          <div class="show-taget">
            <span>审批人：</span>
            <img
              v-if="assessorItem.avatar"
              :src="assessorItem.avatar"
              class="avatar"
              alt=""
            />
            <CAvatar
              v-else
              class="avatar"
              :currentBgc="assessorItem.creatorAvatarColor"
              :name="assessorItem.userName"
            ></CAvatar>
            <span class="approver-name">
              <el-tooltip
                class="item"
                effect="dark"
                :content="assessorItem.userName"
                placement="top"
                :disabled="assessorItem.userName.length < 5"
              >
                <span
                  >{{ assessorItem.userName.slice(0, 4)
                  }}<span v-if="assessorItem.userName.length >= 5">...</span>
                </span>
              </el-tooltip>
            </span>
          </div>
          <div class="switch-box">
            <div class="switch">
              <i class="iconfont icon-switch"></i>
            </div>
          </div>
        </div>
      </CSelect>
      <div v-else class="approver" :class="{ 'no-change': id }">
        <div v-if="assessorItem" class="show-taget">
          <span>审批人：</span>
          <img
            v-if="assessorItem.avatar"
            :src="assessorItem.avatar"
            class="avatar"
            alt=""
          />
          <CAvatar
            v-else
            class="avatar"
            :currentBgc="assessorItem.creatorAvatarColor"
            :name="assessorItem.userName"
          ></CAvatar>
          <span class="approver-name">
            <el-tooltip
              class="item"
              effect="dark"
              :content="assessorItem.userName"
              placement="top"
              :disabled="assessorItem.userName.length < 5"
            >
              <span
                >{{ assessorItem.userName.slice(0, 4)
                }}<span v-if="assessorItem.userName.length >= 5">...</span>
              </span>
            </el-tooltip>
          </span>
        </div>
      </div>
      <el-button size="small" class="cancel" @click="onCancelClick"
        >取消</el-button
      >
      <el-button
        :loading="submitLoading"
        type="primary"
        size="small"
        @click="onSubmitClick"
        >{{ id ? '重新提交' : '提交' }}</el-button
      >
    </div>
    <div class="awards-request-create">
      <div class="create-item create-left">
        <course-card title="基础信息" :afterClass="true">
          <c-dynamic-form
            ref="cDynamicForm"
            class="plan-form"
            :form-data="formData"
            :form-items="formItems"
            :form-attrs="formAttrs"
          >
          </c-dynamic-form>
        </course-card>
      </div>
      <div class="create-item create-right">
        <course-card
          title="获奖证明"
          :afterClass="true"
          class="award-certificate"
        >
          <div class="icon">*</div>
          <b-file-upload
            class="upload-box"
            v-model="formData.evidentialResourceList"
            :canEdit="true"
            :limit="5"
          ></b-file-upload>
        </course-card>
        <course-card title="获奖成果" :afterClass="true">
          <b-file-upload
            class="upload-box"
            v-model="formData.achievementResourceList"
            :canEdit="true"
            :limit="5"
          ></b-file-upload>
        </course-card>
      </div>
    </div>
  </div>
</template>

<script>
import { CDynamicForm } from 'hlcomponents';
import BFileUpload from '@/components/business/BFileUpload';
import {
  getAwardsDict,
  postAddAwardRequest,
  getAwardRequestInfo,
  postEditAwardRequest,
  userTeachGroupLeaders
} from '@/api/awards';
import cloneDeep from 'lodash/cloneDeep';
import {
  validateFiles,
  converterResource
} from '@/components/business/BFileList/util';
import { getTeachPlanGetSubject } from '@/api/school';
import CourseCard from '@/components/common/CourseNameCard';
import CSelect from '@/components/common/CSelect';
import CAvatar from '@/components/common/CAvatar';
import { AwardTypeMap } from '../../utils/enum';
export default {
  name: 'AwardsRequestCreate',
  components: {
    CDynamicForm,
    BFileUpload,
    CourseCard,
    CSelect,
    CAvatar
  },
  props: {
    data: {
      type: Object
    }
  },
  data() {
    return {
      pageLoading: false,
      submitLoading: false,

      // 表单配置
      formAttrs: {
        size: 'medium',
        inline: false,
        // 表单验证
        rules: {
          awardType: [
            {
              required: true,
              message: '请选择奖项类型！',
              trigger: 'blur'
            }
          ],
          awardSubType: [
            {
              required: true,
              message: '请选择活动类型！',
              trigger: 'blur'
            }
          ],
          awardName: [
            {
              required: true,
              message: '请输入奖项名称！',
              trigger: 'blur'
            },
            {
              max: 50,
              message: '奖项名称最多支持50个汉字或字符!',
              trigger: 'blur'
            }
          ],
          awardDate: [
            {
              required: true,
              message: '请选择获奖日期!',
              trigger: 'change'
            }
          ],
          awardLevel: [
            {
              required: true,
              message: '请选择奖项级别！',
              trigger: 'blur'
            }
          ],
          awardRank: [
            {
              required: true,
              message: '请选择奖项名次！',
              trigger: 'blur'
            }
          ],
          awardSubjectId: [
            {
              required: true,
              message: '请选择奖项学科！',
              trigger: 'blur'
            }
          ]
        }
      },
      // 表单数据
      formData: {
        awardName: '',
        comment: '',
        awardRank: '',
        achievementResourceList: [],
        evidentialResourceList: []
      },

      // 奖项类型列表 从接口来 用于级联选择
      awardTypeList: [],

      // 奖项学科列表 从接口来
      subjectList: [],

      // 教研组组长列表 从接口来
      teachGroupLeaders: [],
      // 默认评审人
      assessorItem: null,
      // 打开审批人筛选框
      isApproverSelectOpen: false
    };
  },
  computed: {
    id() {
      let { id } = this.$route.query;
      return id;
    },
    currentTypeObj() {
      return (
        this.awardTypeList?.find(
          item => item.name === this.formData.awardType
        ) || {}
      );
    },
    // 动态表单项
    formItems() {
      return [
        {
          label: '奖项类型',
          prop: 'awardType',
          type: 'select',
          valueKey: 'name',
          labelKey: 'desc',
          options: this.awardTypeList,
          attrs: {
            placeholder: '请选择奖项类型',
            clearable: true
          },
          events: {
            change: value => {
              // 处理 isFirstAuthor
              const flag = [
                AwardTypeMap['paper'].key,
                AwardTypeMap['research'].key
              ].includes(value);
              if (flag) {
                this.formData.isFirstAuthor = 1;
              } else {
                delete this.formData.isFirstAuthor;
              }
              // 处理 奖项名次
              this.formData.awardRank = '';
            }
          }
        },
        {
          type: 'custom',
          hidden: [
            AwardTypeMap['paper'].key,
            AwardTypeMap['research'].key,
            AwardTypeMap['teach'].key
          ].includes(this.formData.awardType)
        },
        {
          label: '第一作者',
          prop: 'isFirstAuthor',
          type: 'radio',
          options: [
            {
              label: '是',
              value: 1
            },
            {
              label: '否',
              value: 0
            }
          ],
          hidden: this.formData.awardType !== AwardTypeMap['paper'].key
        },
        {
          label: '负责人',
          prop: 'isFirstAuthor',
          type: 'radio',
          options: [
            {
              label: '是',
              value: 1
            },
            {
              label: '否',
              value: 0
            }
          ],
          hidden: this.formData.awardType !== AwardTypeMap['research'].key
        },
        {
          label: '活动类型',
          prop: 'awardSubType',
          type: 'select',
          valueKey: 'name',
          labelKey: 'desc',
          options: this.currentTypeObj.subTypeList || [],
          attrs: {
            placeholder: '请选择活动类型',
            clearable: true
          },
          hidden: this.formData.awardType !== AwardTypeMap['teach'].key
        },
        {
          label:
            (this.formData.awardType &&
              AwardTypeMap[this.formData.awardType].awardName) ||
            '奖项名称',
          prop: 'awardName',
          type: 'input',
          attrs: {
            placeholder: '请输入奖项名称'
          }
        },
        {
          label: '获奖日期',
          prop: 'awardDate',
          type: 'datePicker',
          attrs: {
            type: 'date',
            'unlink-panels': true,
            'prefix-icon': 'el-icon-date',
            format: 'yyyy-MM-dd',
            placeholder: '请选择获奖日期',
            'value-format': 'yyyy-MM-dd',
            'picker-options': {
              disabledDate(time) {
                return time.getTime() > Date.now(); // 可选历史天/当前天、不可选未来天
              }
            }
          }
        },
        {
          label: '奖项级别',
          prop: 'awardLevel',
          type: 'select',
          valueKey: 'name',
          labelKey: 'desc',
          options: this.currentTypeObj.levelList || [],
          attrs: {
            placeholder: '请选择奖项级别',
            clearable: true
          }
        },
        {
          label: '奖项名次',
          prop: 'awardRank',
          type: 'select',
          valueKey: 'name',
          labelKey: 'desc',
          options: this.currentTypeObj.rankList || [],
          attrs: {
            placeholder: '请选择奖项名次',
            clearable: true
          }
        },
        {
          label: '奖项学科',
          prop: 'awardSubjectId',
          type: 'select',
          valueKey: 'subjectId',
          labelKey: 'subjectName',
          options: this.subjectList,
          attrs: {
            placeholder: '请选择奖项学科',
            clearable: true
          }
        },
        {
          prop: 'comment',
          type: 'input',
          label: '备注',
          attrs: {
            type: 'textarea',
            placeholder: '请输入',
            resize: 'none',
            'show-word-limit': true,
            maxlength: 500,
            rows: 4
          }
        }
      ];
    }
  },
  watch: {},
  async created() {
    const { isHomeTo } = this.$route.query;
    if (isHomeTo) {
      this.$route.meta.back = '/myHome';
    }

    this.ajaxGetAwardsDict();
    this.ajaxTeachPlanGetSubject();

    // 存在 id 则为编辑，需要回显数据
    if (this.id) {
      // 返回的页面不同，改修属性
      this.$route.meta.title = '奖项编辑';
      this.$route.meta.back =
        '/personal-space/request-awards/info?id=' + this.id;
      await this.ajaxGetAwardDetail();
    } else {
      this.$route.meta.title = '奖项申报';
      this.$route.meta.back = '/personal-space/request-awards';
    }

    this.ajaxUserTeachGroupLeaders();
  },
  mounted() {},
  methods: {
    // 获取奖项字典
    async ajaxGetAwardsDict() {
      const { data } = await getAwardsDict();
      this.awardTypeList = data.awardTypeList;
    },

    // 获取用户所在教研组的所有组长
    async ajaxUserTeachGroupLeaders() {
      try {
        const { data } = await userTeachGroupLeaders();
        this.teachGroupLeaders = data?.users;
        if (this.id && data.users) {
          // 为了重新审批时，能准确找到后端返回的指定审批人
          this.assessorItem = data.users.find(
            item =>
              item.teachGroupId === this.formData.teachGroupId &&
              item.userId === this.formData.auditorId
          );
          if (this.assessorItem) {
            return;
          }
        }
        this.assessorItem = data.users[0];
      } catch (e) {
        this.$log.warn('ajax ajaxUserTeachGroupLeaders error', e);
      }
    },

    // 评审人筛选框选中回调
    setSelectTargetItem(item) {
      this.assessorItem = item;
    },

    // 获取奖项学科列表
    async ajaxTeachPlanGetSubject() {
      const { data } = await getTeachPlanGetSubject();
      this.subjectList = data.subjectList;
    },

    // 查询奖项详情
    async ajaxGetAwardDetail() {
      this.pageLoading = true;
      let { data } = await getAwardRequestInfo({
        awardApplyId: this.id
      });
      this.pageLoading = false;

      data.evidentialResourceList = converterResource(
        data.evidentialResourceList
      );
      data.achievementResourceList = converterResource(
        data.achievementResourceList
      );
      this.formData = data;
      // this.$log.info('👍👍👍👍详情数据', this.formData);
      if (this.formData.auditorGroupDeleted) {
        this.$message.warning('您所在的教研组已被删除，请重新选择审批人');
      }
    },

    // 点击取消按钮
    onCancelClick() {
      // 空表单直接跳转
      if (this.isEmptyForm(this.formData)) {
        this.$router.go(-1);
        return;
      }
      this.$confirm('是否确认取消？已填写的内容将不会保存！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'special-message-box'
      })
        .then(_ => {
          this.$router.go(-1);
        })
        .catch(err => {});
    },
    // 是否是空白表单 true为空白
    isEmptyForm(obj) {
      const res = Object.fromEntries(
        Object.entries(obj).filter(([key, value]) => {
          return (
            (value !== '' && !Array.isArray(value)) ||
            (Array.isArray(value) && value.length > 0)
          );
        })
      );
      return Object.keys(res).length === 0;
    },

    // 点击提交按钮
    async onSubmitClick() {
      // this.$log.info('👍👍👍👍校验前', this.formData);
      if (!(await this.checkFormStatus())) {
        //校验不通过
        return;
      }
      // 校验完毕 可以开始上传了
      this.submitLoading = true;
      // 整理参数
      let params = cloneDeep(this.formData);
      params.teachGroupId = this.assessorItem.teachGroupId;
      params.auditorId = this.assessorItem.userId;
      !params.awardSubType && (params.awardSubType = 'other');
      params.awardDate = params.awardDate.split(' ')[0];
      params.evidentialResourceList = this.transFileArr(
        params.evidentialResourceList
      );
      params.achievementResourceList = this.transFileArr(
        params.achievementResourceList
      );
      params.source = 'pc';
      // this.$log.info('👍👍👍👍处理后', params);
      this.$confirm('是否确认提交奖项申报？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'special-message-box'
      })
        .then(async () => {
          this.id
            ? await postEditAwardRequest(params)
            : await postAddAwardRequest(params);
          this.submitLoading = false;
          this.$message.success(
            `${
              this.id
                ? '已重新提交审核，请耐心等待审核结果！'
                : '已提交审核，请耐心等待审核结果！'
            }`
          );
          this.$router.push({
            path: '/personal-space/request-awards'
          });
        })
        .catch(err => {
          this.submitLoading = false;
        });
    },
    // 检查表单状态 true 为可以提交了 false 或 undefined 不行
    async checkFormStatus() {
      let flag = true;
      const baseCheck = await this.$refs.cDynamicForm
        .invokeFormFn('validate')
        .then(() => true)
        .catch(() => false);
      if (!baseCheck) {
        return;
      }
      // 获奖证明必填校验
      if (!this.formData.evidentialResourceList.length) {
        this.$message.warning('请上传获奖证明！');
        return;
      }

      // 文件是否已准备完毕 true为准备好了
      const isFileReady = (fileArr, fileBelong) => {
        let flag = true;
        if (!validateFiles(cloneDeep(fileArr))) {
          this.$message.warning(`${fileBelong}文件正在上传中，请稍后！`);
          flag = false;
        } else if (!this.$util.checkoutFiles(fileArr)) {
          this.$message.warning(`${fileBelong}存在上传未完成或失败的文件`);
          flag = false;
        }
        return flag;
      };
      // 获奖证明文件状态校验
      if (!isFileReady(this.formData.evidentialResourceList, '获奖证明')) {
        return;
      }
      //获奖成果文件状态校验
      if (!isFileReady(this.formData.achievementResourceList, '获奖成果')) {
        return;
      }
      // this.$log.info('👍👍👍👍 checkFormStatus flag', flag);
      return flag; // 能挺到这里说明校验通过了
    },
    // 整理文件格式为后端想要的格式
    transFileArr(fileArr) {
      const temFileArr = validateFiles(cloneDeep(fileArr)) || [];
      return temFileArr.map(item => {
        return {
          pubResourceFormat: item.resourceFormat,
          pubResourceName: item.resourceName,
          pubResourceUrl: item.resourceUrl
        };
      });
    }
  }
};
</script>

<style lang="less" scoped>
.page-box {
  // 相对 nav-btn-wrap
  position: relative;

  // 按钮区域
  .nav-btn-wrap {
    position: absolute;
    top: -50px;
    right: 0;
    display: flex;
    align-items: center;

    .c-select:hover {
      cursor: pointer;

      .approver {
        border-color: #5c6bd0;
      }
    }
    .approver {
      width: 207px;
      height: 36px;
      line-height: 36px;
      border-radius: 8px;
      border: 1px solid #e0e0e0;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      box-sizing: border-box;
      .show-taget {
        flex: 1;
        display: flex;
        align-items: center;
        position: relative;
        border-radius: 5px 0 0 5px;
        padding-left: 12px;

        .approver-name {
          flex: 1;
          font-weight: 500;
        }
        .avatar {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          margin-right: 8px;
        }
      }

      .switch-box {
        width: 36px;
        height: 36px;
        display: flex;
        border-radius: 0 5px 5px 0;
        cursor: pointer;
        .switch {
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          background: #f8f8f8;
          border-radius: 5px;
          margin: auto;
          .icon-switch {
            display: inline-block;
            font-size: 12px;
            transform: scale(0.8);
          }
        }
        .switch:hover {
          background: #e6e6e6;
        }
      }
    }
    .no-change {
      width: unset;
      max-width: 186px;
      .approver-name {
        padding-right: 15px;
      }
    }
    .el-button {
      width: 80px;
      height: 36px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      padding: 0;
      border-radius: 8px;
    }
    .cancel {
      margin-left: 10px;
    }
  }

  // 内容区域布局
  .awards-request-create {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    .create-item {
      width: 50%;
    }
    .create-left {
    }
    .create-right {
      .upload-box {
        margin-top: 14px;
      }
      .award-certificate {
        position: relative;
        margin-bottom: 20px;
        .icon {
          position: absolute;
          top: 22px;
          left: 98px;
          font-size: 16px;
          font-weight: 500;
          color: #f04444;
        }
      }
    }
  }

  // 表单样式
  /deep/ .plan-form {
    padding-top: 4px;
    margin-bottom: -12px;

    .el-form-item {
      display: inline-block;
      margin-bottom: 12px;
      width: calc(50% - 10px);
      .el-form-item__label {
        color: rgba(0, 0, 0, 0.45);
      }
      .el-form-item__content {
        .el-date-editor {
          width: 100%;
        }
        .el-select {
          width: 100%;
        }
        .el-radio-group {
          width: 100%;

          .el-radio {
            width: calc(50% - 5px);
            padding: 9px 13px;
            margin: 0;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            box-sizing: border-box;

            &:not(:first-child) {
              margin-left: 10px;
            }

            &.is-checked {
              border-color: #5c6bd0;
            }
          }
        }
        .el-textarea__inner:focus {
          border-color: #dcdfe6 !important;
        }
      }
      // 左边的一列
      &:nth-child(1),
      &:nth-child(4),
      &:nth-child(6) {
        margin-right: 20px;
      }
      // 独占一行的
      &:nth-of-type(3),
      &:nth-of-type(8) {
        width: 100%;
      }
      // 获奖日期特殊处理
      &:nth-of-type(4) {
        .el-input__inner {
          padding-left: 15px;
        }
        .el-input__prefix {
          left: auto;
          right: 5px;
        }
      }
    }
  }
}
</style>
