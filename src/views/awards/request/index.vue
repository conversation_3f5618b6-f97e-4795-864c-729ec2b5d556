<!-- 奖项申报 -->
<template>
  <div class="page-award-request page-viewer-masking">
    <!-- 奖项总览 start -->
    <div class="sum-bar">
      <div v-for="(item, index) in totalNumArr" :key="index" class="bar-box">
        <div class="bar-box-member">
          <div class="member-left">
            <span class="member-left-text">{{ item.text }}</span>
            <span class="member-left-num">{{ item.count }}</span>
          </div>
          <div class="member-right">
            <img :src="item.image" class="member-right-img" />
          </div>
        </div>
        <div class="bar-box-operator" v-if="index !== totalNumArr.length - 1">
          {{ item.type === 'ALL' ? '=' : '+' }}
        </div>
      </div>
    </div>
    <!-- 奖项总览 end -->
    <!-- 表格 start-->
    <div class="proTable-wrap">
      <CProTable
        ref="CProTableRef"
        title="奖项申报列表"
        :formColumns="formColumns"
        :columns="columns"
        :api="ajaxGetRewardsList"
        :emptyPage="{ tip: '暂无数据', type: '3' }"
        @row-click="onDetailClick"
        routeName="/personal-space/request-awards"
      >
        <template slot="titleRight">
          <div class="right-operation">
            <div class="right-operation-btn" @click="onResetClick">
              重置筛选
            </div>
            <div
              class="right-operation-btn active-style"
              @click="onCreatedClick"
            >
              + 新增奖项
            </div>
          </div>
        </template>
      </CProTable>
    </div>
    <!-- 表格 end-->
  </div>
</template>

<script>
import {
  getAwardsDict,
  getAwardsRequestList,
  getAwardsTotal
} from '@/api/awards';
import CProTable from '@/components/common/CProTable';
import { ApplyStatusMap } from '../utils/enum';
import { MESSAGE_CENTER_BUSINESS_TYPE } from '@/plugins/enum';

export default {
  name: 'RequestAwards',
  components: {
    CProTable
  },
  data() {
    return {
      // 奖项总览
      totalNumArr: [],

      awardDictArr: [], // 接口来的
      // 用于表格数据展示
      awardTypeMap: {},
      awardLevelMap: {},
      awardRankMap: {},
      ApplyStatusMap,

      // 表格配置项
      columns: [
        {
          label: '奖项类型',
          prop: 'awardType',
          minWidth: 150,
          search: {
            type: 'tableMultiSelect',
            prop: 'awardTypeList',
            props: {
              options: []
            }
          },
          render: row => {
            return (
              <div class="reward-dot_warp">
                {this.awardTypeMap[row.awardType]}
                {row.hasRedDot && <span class="is-read-style"></span>}
              </div>
            );
          }
        },
        {
          label: '奖项名称',
          prop: 'awardName',
          minWidth: 150,
          showOverflowTooltip: true,
          search: {
            type: 'tableInput',
            prop: 'awardNameLike',
            props: {
              placeholder: '请输入奖项名称'
            }
          }
        },
        {
          label: '获奖级别',
          prop: 'awardLevel',
          minWidth: 120,
          search: {
            type: 'tableMultiSelect',
            prop: 'awardLevelList',
            props: {
              options: []
            }
          },
          render: row => {
            return <div>{this.awardLevelMap[row.awardLevel]}</div>;
          }
        },
        {
          label: '获奖名次',
          prop: 'awardRank',
          render: row => {
            return <div>{this.awardRankMap[row.awardRank]}</div>;
          }
        },
        {
          label: '获奖日期',
          prop: 'awardDate',
          sortable: 'custom',
          sortBy: 'awardDateSort',
          minWidth: 120,
          render: row => <div>{row.awardDate.split(' ')[0]}</div>
        },
        {
          label: '审核状态',
          prop: 'auditStatus',
          minWidth: 130,
          search: {
            type: 'tableMultiSelect',
            prop: 'auditStatusList',
            props: {
              options: Object.keys(ApplyStatusMap).map(key => ({
                label: ApplyStatusMap[key].value,
                value: key
              }))
            }
          },
          render: row => {
            return (
              <span class={`audit-tag audit-tag-${row.auditStatus}`}>
                {ApplyStatusMap[row.auditStatus].value}
              </span>
            );
          }
        },
        {
          label: '操作',
          colType: 'btns',
          btns: [
            {
              label: '查看',
              click: this.onDetailClick
            }
          ]
        }
      ],
      formColumns: [
        { type: 'tableFormDateSelect', label: '获奖日期', prop: 'awardDate' }
      ],
      // 表格筛选参数
      totalParams: {}
    };
  },
  watch: {
    // // 奖项类型联动获奖级别
    // 'totalParams.awardTypeList': {
    //   handler(val = []) {
    //     this.setColumnsOptions(2, this.getKeyDataFromDict('levelList', val)); // 填充 获奖级别
    //   }
    // },
    totalParams: {
      handler() {
        this.ajaxGetAwardsTotal(); // 一进页面就会执行了
      },
      deep: true
    }
  },
  mounted() {
    this.doInit();
    this.$bus.$on(MESSAGE_CENTER_BUSINESS_TYPE.AWARD_AUDIT, () => {
      this.$nextTick(() => {
        this.$refs.CProTableRef && this.$refs.CProTableRef.getList();
      });
    });
  },
  methods: {
    // 初始化
    async doInit() {
      await this.ajaxGetAwardsDict();
      this.setColumnsOptions(0, this.awardDictArr); // 填充 奖项类型
      this.awardTypeMap = this.handleToMap(this.awardDictArr); // 生成 最全 奖项类型 map

      const awardLevelData = this.getKeyDataFromDict('levelList');
      this.setColumnsOptions(2, awardLevelData); // 填充 获奖级别
      this.awardLevelMap = this.handleToMap(awardLevelData); // 生成 最全 奖项级别 map

      const awardRankData = this.getKeyDataFromDict('rankList');
      this.awardRankMap = this.handleToMap(awardRankData); // 生成 最全 奖项名次 map
    },

    // 获取统计数据 并处理
    async ajaxGetAwardsTotal() {
      const filterData = { ...this.totalParams };
      // 处理参数
      delete filterData.page;
      delete filterData.pageSize;
      // TODO:这几个删不删还要问下产品 后端的意思是删
      filterData.awardDateFrom && delete filterData.awardDateFrom;
      filterData.awardDateTo && delete filterData.awardDateTo;
      filterData.awardNameLike && delete filterData.awardNameLike;
      let { data } = await getAwardsTotal(filterData);
      this.totalNumArr = [
        {
          type: 'ALL',
          text: '总申请数',
          count: data.total,
          image: require('@/assets/images/awards/total.png')
        },
        {
          type: 'MEMBER',
          text: '教学活动',
          count: data.teachNum,
          image: require('@/assets/images/awards/activity.png')
        },
        {
          type: 'MEMBER',
          text: '论文著作',
          count: data.paperNum,
          image: require('@/assets/images/awards/paper.png')
        },
        {
          type: 'MEMBER',
          text: '课题研究',
          count: data.researchNum,
          image: require('@/assets/images/awards/research.png')
        },
        {
          type: 'MEMBER',
          text: '先进荣誉',
          count: data.honorNum,
          image: require('@/assets/images/awards/advanced.png')
        },
        {
          type: 'MEMBER',
          text: '其他',
          count: data.otherNum,
          image: require('@/assets/images/awards/other.png')
        }
      ];
    },

    // 获取奖项字典
    async ajaxGetAwardsDict() {
      const { data } = await getAwardsDict();
      this.awardDictArr = data.awardTypeList;
    },
    // 字典填充 columns index 是位置 data是要填充的数据
    setColumnsOptions(index, data) {
      const transData = data.map(item => {
        item.label = item.desc;
        item.value = item.name;
        return item;
      });
      // this.$log.info('👍👍👍👍11', data, transData);
      this.columns[index].search.props.options = transData;
    },
    // 获取map
    getKeyDataFromDict(targetKey, awardTypeVal) {
      let targetTypeArr = [...this.awardDictArr];
      if (awardTypeVal) {
        //筛选
        targetTypeArr = this.awardDictArr.filter(type =>
          awardTypeVal.includes(type.name)
        );
      }
      // 平铺
      const temArr = targetTypeArr.map(obj => obj[targetKey]).flat();
      // 去重
      return Array.from(
        new Map(temArr.map(item => [item.name, item])).values()
      );
    },
    // 数组转map
    handleToMap(arr) {
      return arr.reduce((acc, obj) => {
        acc[obj.name] = obj.desc;
        return acc;
      }, {});
    },

    // 获取奖项列表
    async ajaxGetRewardsList(params) {
      this.totalParams = this.handleFilterData(params);
      const { data } = await getAwardsRequestList(this.totalParams);
      return {
        list: data.awardApplyList,
        total: data.pagination.allCount
      };
    },
    // 处理得到筛选条件
    handleFilterData(obj) {
      // 过滤掉值空字符串 / 数组为空
      const res = Object.fromEntries(
        Object.entries(obj).filter(([, value]) => {
          return (
            (value !== '' && !Array.isArray(value)) ||
            (Array.isArray(value) && value.length > 0)
          );
        })
      );
      // 处理时间
      if (res.awardDate) {
        res.awardDateFrom = res.awardDate[0] + ' 00:00:00';
        res.awardDateTo = res.awardDate[1] + ' 23:59:59';
        delete res.awardDate;
      }
      // this.$log.info('执行🐱‍💻🐱‍💻处理参数', res);
      return res;
    },

    // 提供给外部用的 如果计划改变了 调用这个
    triggerSearch() {
      this.$refs.CProTableRef.getList();
    },
    // 重置
    onResetClick() {
      this.$refs.CProTableRef.resetAll();
    },
    // 新增奖项
    onCreatedClick() {
      this.$router.push('/personal-space/request-awards/create');
    },
    // 查看详情
    onDetailClick(row) {
      this.$router.push({
        path: '/personal-space/request-awards/info',
        query: {
          id: row.awardApplyId
        }
      });
      // 去除未读小红点儿
      row.seeTypeCode = 1;
    }
  }
};
</script>

<style lang="less" scoped>
.page-award-request {
  // 统计部分
  .sum-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .bar-box:last-child {
      flex-basis: 184px;
    }

    .bar-box {
      flex: 1 1 calc(184px + 40px);
      display: flex;
      align-items: center;

      .bar-box-member {
        flex: 1 1 184px;
        height: 99px;
        box-sizing: border-box;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 10px;
        border: 1px solid rgba(224, 224, 224, 0.5);
        backdrop-filter: blur(10px);

        padding: 20px;
        padding-right: 10px;

        display: flex;
        align-items: center;
        justify-content: space-between;

        .member-left {
          flex: 1;

          &-text {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.45);
            line-height: 20px;
          }

          &-num {
            display: block;
            margin-top: 8px;
            font-size: 24px;
            font-family: DINPro-M;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            line-height: 31px;
          }
        }

        .member-right {
          width: 81px;
          height: 55px;

          &-img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .bar-box-operator {
        flex: 1 1 40px;
        text-align: center;
        font-size: 30px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.25);
      }
    }
  }

  .proTable-wrap {
    margin-top: 25px;
    // 筛选框
    .right-operation {
      display: flex;
      align-items: center;

      &-btn {
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);
        height: 36px;
        line-height: 34px;
        padding: 0 15px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        box-sizing: border-box;
        cursor: pointer;
        user-select: none;
        margin-left: 10px;

        &[data-disabled] {
          cursor: not-allowed;
        }

        &.active-style:focus,
        &.active-style:hover {
          background-color: #eff0fa;
          border-color: #ced3f1;
          color: #5c6bd0;
        }
      }
    }
    // 消息红点儿
    .reward-dot_warp {
      position: relative;
      .is-read-style {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        margin-left: 10px;
        display: inline-block;
        width: 6px;
        height: 6px;
        background: #f04444;
        border-radius: 50%;
      }
    }
    // 审核状态标签
    .audit-tag {
      border-radius: 14px;
      font-size: 12px;
      font-family: CangerYuYang-W04;
      font-weight: normal;
      line-height: 15px;
      padding: 4px 10.5px;
      box-sizing: border-box;

      &::before {
        content: '';
        display: inline-block;
        width: 7px;
        height: 7px;
        border-radius: 50%;
        margin-right: 4px;
      }

      // 审核中
      &-unaudited {
        background: rgba(224, 162, 52, 0.1);
        color: #e0a234;
        &::before {
          background: rgba(224, 162, 52, 1);
        }
      }

      // 通过
      &-pass {
        background: rgba(34, 148, 83, 0.1);
        color: #229453;
        &::before {
          background: rgba(34, 148, 83, 1);
        }
      }

      // 未通过
      &-notPass {
        background: rgba(128, 118, 110, 0.1);
        color: #80766e;
        &::before {
          background: rgba(128, 118, 110, 1);
        }
      }
    }
  }
}
</style>
