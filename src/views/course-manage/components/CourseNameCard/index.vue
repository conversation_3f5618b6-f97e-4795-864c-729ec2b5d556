<template>
  <div class="course-name-card-wrap">
    <h3 :class="{ after: afterClass }">
      <span>{{ title }}</span>
      <span class="required-mark" v-if="required">*</span>
      <slot name="header"></slot>
    </h3>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'CourseCard',
  props: {
    title: String,
    afterClass: {
      type: <PERSON>olean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style lang="less" scoped>
.course-name-card-wrap {
  background: #ffffff;
  border-radius: 10px;
  border: 1px solid #e0e0e0;
  padding: 20px;

  h3 {
    span {
      font-size: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      line-height: 30px;
    }

    .required-mark {
      color: #ff0000;
      margin-left: 4px;
      font-size: 16px;
      line-height: 18px;
    }

    display: flex;
    align-items: center;
  }
  .after {
    position: relative;
    padding-left: 10px;
    &::after {
      content: '';
      position: absolute;
      top: 7.5px;
      left: 0;
      width: 3px;
      height: 14px;
      border-radius: 1.5px;
      background: #878fc8;
    }
  }
  .box {
    padding: 20px 0;
  }
}
</style>
