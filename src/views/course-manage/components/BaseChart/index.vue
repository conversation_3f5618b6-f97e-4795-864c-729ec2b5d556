<!--
 * @Author: wangxu <EMAIL>
 * @Date: 2022-01-28 13:37:16
 * @LastEditors: wangxu <EMAIL>
-->
<template>
  <!-- 图表容器 -->
  <div class="echarts"></div>
</template>

<script>
// 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
import * as echarts from 'echarts/core';
// 防抖
import debounce from 'lodash/debounce';
// 监听
import { addListener, removeListener } from 'resize-detector';

export default {
  name: 'BaseNightingaleChart',
  props: {
    /**
     * 图表数据
     */
    data: {
      type: Array,
      default: () => []
    },
    /**
     * @description: 是否启用轮播
     */
    slideshow: {
      type: Boolean,
      default: false
    },
    /**
     * 启用自动自适应
     */
    autoResize: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      seriesData: [], // 数据项
      activeIndex: -1, // 当前激活下标
      timeObj: null, // 定时器对象
      interval: 5000 // 间隔毫秒数
    };
  },
  mounted() {
    // 初始化图表
    this.init();
    window.addEventListener('resize', this.onResizeEcharts);
  },
  destroyed() {
    this.onClearInterval();
    window.removeEventListener('resize', this.onResizeEcharts);
  },
  watch: {
    options: 'handleOptionsChange'
  },
  methods: {
    /**
     * @description: 销毁时，清除定时器
     * @return {*}
     */
    onClearInterval() {
      // this.$log.info('🐸 onClearInterval');
      // this.timeObj = null;
      this.activeIndex = -1;
      clearInterval(this.timeObj);
    },
    /**
     * 初始化图表
     */
    init() {
      // 初始化过无需重新初始化
      if (this.chart) return;
      // 创建echarts实例
      const chart = echarts.init(this.$el);
      chart.setOption(this.options || {}, true);
      this.chart = chart;
      // this.initResizeEvent();
    },
    /**
     * @description: 图表进行轮播
     * @return {*}
     */
    onSetInterval() {
      // 计算下一个高亮区域的下标值
      this.timeObj = setInterval(() => {
        let index =
          this.activeIndex < this.seriesData.length - 1
            ? this.activeIndex + 1
            : 0;
        this.onClickChart(index);
      }, this.interval);
    },
    /**
     * @description: 图表自适应宽高
     * @return {*}
     */
    onResizeEcharts: debounce(function() {
      // this.$log.info('🐸🐸🐸', this.chart);
      this.autoResize && this.chart.resize();
    }, 400),
    /**
     * 初始化窗口变化事件
     */
    initResizeEvent() {
      const self = this;
      // 窗口变化重新渲染
      const __resizeHandler = debounce(() => {
        self.autoResize && self.chart.resize();
      }, 400);
      addListener(this.$el, __resizeHandler);
      this.$on('hook:destroyed', () => {
        removeListener(this.$el, __resizeHandler);
      });
    },
    /**
     * options变化，重新初始化
     */
    handleOptionsChange() {
      this.onClearInterval();
      // this.$log.info('🐸 seriesData', this.seriesData, this.options);
      this.seriesData =
        this.options.series &&
        (this.options.series[0]?.data || this.options.series.data);
      // this.$log.info('🐸 seriesData', this.seriesData, this.options);
      this.chart.setOption(this.options || {});
      // this.$log.info(this.chart.getZr());
      // this.chart.getZr().on('mousemove', param => {
      //   const pointInPixel = [param.offsetX, param.offsetY];
      //   if (this.chart.containPixel('grid', pointInPixel)) {
      //     //若鼠标滑过区域位置在当前图表范围内 鼠标设置为小手
      //     this.chart.getZr().setCursorStyle('pointer');
      //   } else {
      //     this.chart.getZr().setCursorStyle('default');
      //   }
      // });
      if (this.seriesData.length <= 1 || !this.seriesData[0]?.name) {
        // 将值传给父组件
        this.$emit('clickChangeData', this.seriesData[0]);
        return;
      }
      // 是否启用自动轮播
      if (this.slideshow) {
        this.onSlideshow();
      }
    },
    /**
     * @description: 开始轮播
     * @return {*}
     */
    onSlideshow() {
      // 默认第一个被点击
      if (this.activeIndex === -1) {
        this.onClickChart(0);
      }
      // 轮播
      this.onSetInterval();
      const _this = this;
      // 点击后高亮显示悬停的那块
      this.chart.on('click', function(data) {
        // 停止自动轮播
        _this.onClearInterval();
        _this.onClickChart(data.dataIndex);
        // 开启自动轮播
        _this.onSetInterval();
      });
    },
    /**
     * @description: 触发点击事件
     * @return {*}
     */
    onClickChart(index) {
      // 高亮
      this.chart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: index
      });
      this.seriesData.forEach((item, key) => {
        // 将非当前下标区域消除高亮
        if (key !== index) {
          this.chart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: key
          });
        }
      });
      // this.$log.info('🐸 onClickChart', index, this.activeIndex);
      let data = this.seriesData[index];
      // let { name, value, color } = data;
      // this.$log.info(name, value, color);
      // 记录下标值
      this.activeIndex = index;
      // 将值传给父组件
      this.$emit('clickChangeData', data);
    }
  }
};
</script>

<style lang="less" scoped>
.echarts {
  height: 300px;
}
</style>
