import axios from '@/plugins/axios';

/**
 * 获取用户信息
 * @return {Promise}
 */
export function getNewUserInfo() {
  return axios(
    {
      method: 'POST',
      url: '/v1/role_manage/user/get_row'
    },
    'lesson'
  );
}

// 新：获取侧边栏菜单
export function getSideBarMenu() {
  return axios(
    {
      method: 'POST',
      url: '/v1/menu/get_lists'
    },
    'lesson'
  );
}

/**
 * 获取用户详情
 * @return {Promise}
 */
// 迁移前
// export function getUserDetail(id) {
//   return axios(
//     {
//       method: 'get',
//       url: '/staff/detail',
//       params: {
//         staffId: id
//       }
//     },
//     'teach-manage'
//   );
// }
// 迁移后
export function getUserDetail(id) {
  return axios(
    {
      method: 'POST',
      url: '/v1/saas/user/detail',
      data: {
        staffId: id
      }
    },
    'lesson'
  );
}

/**
 * /lesson/v1/saas/login
 */
export function saasLogin(data) {
  return axios({
    method: 'POST',
    url: '/hr/lesson/v1/saas/login',
    data
  });
}

/**
 * 获取飞书APPID
 */
export function getLarkAppId(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/saas/feishu/get_appid',
      data
    },
    'lesson'
  );
}

/**
 * 飞书登录
 */
export function loginByLark(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/saas/feishu/dolog',
      data
    },
    'lesson'
  );
}

/**
 * 获取钉钉APPID
 */
export function getDingtalkAppId(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/saas/ding/get_appkey',
      data
    },
    'lesson'
  );
}

/**
 * 获取钉钉cropId
 */
export function getDingtalkCropId(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/saas/corp_id_get_row',
      data
    },
    'lesson'
  );
}
