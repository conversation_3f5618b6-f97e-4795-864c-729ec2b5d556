import axios from '@/plugins/axios';

/**
 * 【AI听评课】学科列表查询
 * @param {Object} data 参数
 */
export function getSubjectList(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/group_manage/get_all_subject_group_list',
      data
    },
    'lesson'
  );
}

/**
 * 查询校区下所有年级
 * @param {Object} data 参数
 */
export function getGradeList(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/saas/grade/get_all_list',
      data
    },
    'lesson'
  );
}

/**
 * 【课堂档案】课堂分析使用量查询
 * @param {Object} data 参数
 */
export function getAnalysisUsage(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/icourse/usage/get_row',
      data
    },
    'lesson'
  );
}

/**
 * 【课题分析概览】课题分析概览
 * @param {Object} data 参数
 */
export function getAnalysisOverview(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/icourse/analysis/overview',
      data
    },
    'lesson'
  );
}

/**
 * 查询指定校区下的教师列表
 */
export function getTenantUserList(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/school/campus/get_campus_staff_list',
      data
    },
    'lesson'
  );
}

/**
 * 查询校区下所有年级
 */
export function getAllGradeList(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/saas/grade/get_all_list',
      data
    },
    'lesson'
  );
}

/**
 * 【课堂档案】课堂报告全文检索
 */
export function getArchiveSearch(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/icourse/archive/search',
      data
    },
    'lesson'
  );
}
