import axios from '@/plugins/axios';

/**
 * 查询教研日历数据
 * @param {Object} data 参数
 * @param {String} data.scheduleDateFrom 开始时间 yyyy-MM-dd
 * @param {String} data.scheduleDateTo 结束时间 yyyy-MM-dd
 * @param {Boolean} onlyMe 是否仅查看自己
 * @param {String} teachGroupId 教研组筛选
 */
export function getCalendarInfo(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/schedule/get_lists',
      data
    },
    'listen'
  );
}

/**
 * 查询教研日历本周数据
 * @param {Object} data 参数
 * @param {String} data.scheduleDateFrom 开始时间 yyyy-MM-dd
 * @param {String} data.scheduleDateTo 结束时间 yyyy-MM-dd
 * @param {Boolean} onlyMe 是否仅查看自己
 * @param {String} teachGroupId 教研组筛选
 */
export function getWeekCalendarInfo(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/schedule/this_week_get_lists',
      data
    },
    'listen'
  );
}

/**
 * 教研日历-搜索日程活动
 * @param {Object} data 参数
 * @param {String} data.scheduleDateFrom 开始时间 yyyy-MM-dd
 * @param {String} data.scheduleDateTo 结束时间 yyyy-MM-dd
 * @param {String} data.type 活动类型
 * @param {String} data.summaryLike 搜索关键字
 */
export function getSearchScheduleActivities(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/schedule/day/get_lists',
      data
    },
    'listen'
  );
}
/**
 * 新增教研活动
 * @param {Object} data 参数
 */
export function addTeachActivity(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/teach_activity/add',
      data
    },
    'listen'
  );
}

/**
 * 获取用户所属教研组, 按学段分组
 * @param {Object} data 参数
 */
export function getTeachGroupList(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/teach_group_manage/section/get_group_lists',
      data
    },
    'lesson'
  );
}

/**
 * 编辑教研活动
 * @param {Object} data 参数
 */
export function updateTeachActivity(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/teach_activity/edit',
      data
    },
    'listen'
  );
}
/**
 * 查询教研活动详情
 * @param {String} id 活动id
 */
export function getTeachActivityDetail(id) {
  return axios(
    {
      method: 'POST',
      url: '/v1/teach_activity/get_row',
      data: {
        teachActivityId: id
      }
    },
    'listen'
  );
}

/**
 * @description: 教研日历-查询可预约活动
 * @param {*} data
 * @return {*}
 */
export function getScheduleAppointmentList(data) {
  return axios(
    {
      method: 'POST',
      // url: '/teachTaskActivity/openClassQuery',
      url: '/v1/schedule/appointment/get_lists',
      data
    },
    'listen'
  );
}

/**
 * @description: 教研日历-教研活动-预约参加/取消预约
 * @param {*} data
 * @return {*}
 */
export function postChangeActivityStatus(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/teach_activity/appointment',
      data
    },
    'listen'
  );
}

/**
 * 教研日历-公开课-预约参加
 * @param {*} data
 * @returns
 */
export function postAppointmentCourseAdd(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/course/invite/appointment/add',
      data
    },
    'listen'
  );
}

/**
 * 教研日历-公开课-取消预约
 * @param {*} data
 * @returns
 */
export function postAppointmentCourseCancel(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/course/invite/appointment/del',
      data
    },
    'listen'
  );
}

/**
 * @description: 教研活动-日程详情-附件上传
 * @return {*}
 */
export function getTeachGroupUploadAdd(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/resource/add',
      data
    },
    'lesson'
  );
}

/**
 * @description: 教研活动-日程详情-删除附件
 * @return {*}
 */
export function getTeachGroupUploadDel(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/resource/delete',
      data
    },
    'lesson'
  );
}

/**
 * 创建
 * planId: 教学计划id
 * prepareGroupId 备课组id
 * discussName: 研讨名称
 * discussStart: 开始时间
 * discussEnd: 结束时间
 * preparePlanDetailIds: 集备明细
 * discussInventory： 清单
 * discussFileIds: 附件
 */
export function addDiscuss(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/preparation_discussion/add',
      data
    },
    'listen'
  );
}

/**
 * @param {*} params
 * id： 计划
 * discussStart： 开始时间
 * discussEnd： 结束时间
 * preparePlanDetailIds: 数组
 * discussInventory：清单
 * discussFileIds：研讨附件id集合
 * @returns
 */

export function updateDiscuss(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/preparation_discussion/edit',
      data
    },
    'listen'
  );
}

/**
 * 获取集备研讨详情
 * id: 集备研讨id
 * @returns
 */

export function getPreparationDiscussion(id) {
  return axios(
    {
      method: 'POST',
      url: '/v1/preparation_discussion/get_row',
      data: {
        preparationDiscussionId: id
      }
    },
    'listen'
  );
}

/**
 * 获取带教研组分组的备课组下拉
 */
export function getPrepareGroup(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/prepare_group/control/get_lists',
      data
    },
    'lesson'
  );
}

/**
 * @description: 教研日历-删除教研活动
 * @param {*} data
 * @return {*}
 */
export function postRemoveTeachActivity(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/teach_activity/delete',
      data
    },
    'listen'
  );
}

/**
 * @description: 教研日历-删除集备研讨
 * @param {*} data
 * @return {*}
 */
export function postRemovePreparationDiscussion(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/preparation_discussion/delete',
      data
    },
    'listen'
  );
}

/**
 * @description: 获取有管理权限的组/组员
 * @param {*} data
 * @return {*}
 */
export function getPrepareTeacherGroup(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/teach_group_manage/control/get_lists',
      data
    },
    'lesson'
  );
}

/**
 * 获取集备活动中的单元列表
 * @returns
 */
export function getTeachPrepareUnit(id) {
  return axios(
    {
      method: 'POST',
      url: '/v1/preparation_discussion/teach_prepare_unit/get_lists',
      data: {
        preparationDiscussionId: id
      }
    },
    'listen'
  );
}

/**
 * 获取单元模块详情
 * @param {*} id
 * @returns
 */
export function getTeachPrepareUnitModule(id) {
  return axios(
    {
      method: 'POST',
      url: '/v1/prepare/unit/module/get_lists',
      data: {
        teachPreparePlanDetailId: id
      }
    },
    'lesson'
  );
}

/**
 * 编辑单元模块
 * @param {} data
 * @returns
 */
export function setUnitModuleSetting(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/prepare/unit/module/setting',
      data
    },
    'lesson'
  );
}

/**
 * 编辑单元模块内容
 * @param {*} data
 * @returns
 */
export function setUnitModuleContent(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/prepare/unit/module/edit',
      data
    },
    'lesson'
  );
}

/**
 * 设置考勤情况
 * @returns
 */
export function setUserAttendInfo(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/attendance/edit',
      data
    },
    'listen'
  );
}

/**
 * 点赞或疑问
 * @param {*} data
 * schoolId 学校 id
 * campusId 校区 id
 * businessType 点赞类型 1-评论点赞 2-集备资源点赞 3-集备单元模块
 * relationId
 * isFavor 是否点赞
 * isDisFavor 是否疑问
 * @returns
 */
export function toggleLike(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/favor/edit',
      data
    },
    'lesson'
  );
}

/* 获取资源列表
 * @param {*} data
 * @returns
 */
export function getResourceList(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/prepare/resource/get_lists',
      data
    },
    'lesson'
  );
}

/**
 * 设置引导状态
 */
export function setGuideStatus(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/guide/add',
      data
    },
    'lesson'
  );
}

/**
 * 设置引导状态
 */
export function getGuideStatus(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/guide/get_row',
      data
    },
    'lesson'
  );
}

// ------------------- 活动记录 -------------------
/**
 * 导出活动记录
 */
export function exportActivityRecord(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/listen/meeting/export',
      data
    },
    'listen'
  );
}

/**
 * 提交会议记录任务
 */
export function postActivityRecordTask(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/meeting/task/add',
      data
    },
    'lesson'
  );
}

/**
 * 获取会议记录信息（关键词、总结等信息）
 */
export function getActivityRecordInfo(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/meeting/get_row',
      data
    },
    'lesson'
  );
}

/**
 * 获取会议记录内容 （发言人和发言的内容）
 */
export function getActivityRecordContent(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/meeting/paragraph/get_lists',
      data
    },
    'lesson'
  );
}

/**
 * 设置发言人
 */
export function updateSpeaker(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/meeting/participant/edit',
      data
    },
    'lesson'
  );
}

/**
 * 获取指定学期计划下包含快照数据的教研组
 */
export function getTeachGroup(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/groupManage/history/teach_group_get_list',
      data
    },
    'lesson'
  );
}

/**
 * 获取会议录音状态
 */
export function getActivitySoundRecordingInfo(data) {
  return axios(
    {
      method: 'POST',
      url: '/meeting/info',
      data
    },
    'message-center'
  );
}

// 活动检查-教研活动列表
export function getActivityExamList(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/teach_activity/get_list',
      data
    },
    'listen'
  );
}

// 活动检查-教研活动列表-导出
export function doActivityExamExport(data) {
  return {
    config: {
      method: 'POST',
      url: '/v1/teach_activity/export',
      responseType: 'blob',
      data
    },
    type: 'listen'
  };
}

// 活动检查-集备活动列表
export function getSeminarsExamList(data) {
  return axios(
    {
      method: 'POST',
      url: '/v1/preparation_discussion/get_list',
      data
    },
    'listen'
  );
}

// 活动检查-集备活动列表-导出
export function doSeminarsExamExport(data) {
  return {
    config: {
      method: 'POST',
      url: '/v1/preparation_discussion/export',
      responseType: 'blob',
      data
    },
    type: 'listen'
  };
}

// 根据活动id查询附件信息
export function getFileListByBusinessId(relationIdList) {
  const data = { relationIdList };
  return axios(
    {
      method: 'POST',
      url: '/v1/resource/get_lists',
      data
    },
    'lesson'
  );
}
