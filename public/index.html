<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="<%= BASE_URL %>sys-logo.png">
  <title>鸿儒教研</title>
  <meta property="og:title" content="鸿儒教研">
  <meta property="og:url" content="hrjy.hailiangedu.com">
  <meta property="og:image" content="<%= BASE_URL %>sys-logo.png">
  <meta property="og:description" content="让教研管理更透明，让教师工作更高效">
  <script src="https://hl-common-prod.obs.cn-east-3.myhuaweicloud.com/web/web-track/latest/index.js">
  </script>
  <!-- <script src="https://hl-common-prod.obs.cn-east-3.myhuaweicloud.com/hrjy/track/index.js">
  </script> -->
  <!-- 引入微信扫码登录 -->
  <script src="https://hl-common-prod.obs.cn-east-3.myhuaweicloud.com/web/web-assets/js/wechatQRLogin.js"></script>
  <!-- 引入飞书扫码登录 -->
  <script
    src="https://sf3-cn.feishucdn.com/obj/feishu-static/lark/passport/qrcode/LarkSSOSDKWebQRCode-1.0.2.js"></script>
  <!-- 引入钉钉扫码登录 -->
  <script src="https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js"></script>



  <link
    href="<%= VUE_APP_SLS_NODE_ENV==='production'? 'https://hl-common-prod.obs.cn-east-3.myhuaweicloud.com/hrjy/webStatic/vue.min.js' : 'https://hl-test-ca.obs.cn-east-3.myhuaweicloud.com/hrjy/webStatic/vue.min.js' %>"
    rel="preload" as="script">
  <!-- <script src="https://hl-common-prod.obs.cn-east-3.myhuaweicloud.com/web/web-track/1.0.1-beta/index.js"></> -->

  <!-- 引入 jessibuca 播放器 （可播放 flv h265）-->
  <!-- jessibuca播放器文档 https://jessibuca.com -->
  <script src="/jessibuca/jessibuca.js"></script>
  <script>
    window.$track = new WebTrack({
      env: '<%= VUE_APP_SLS_NODE_ENV %>',
      config: {
        logstore: '<%= VUE_APP_SLS_LOG_STORE %>',
      },
      watchEvents: {
        clickMount: true,
        pageView: true
      },
      terminalInfo: {
        platform: /AppleWebKit.*(Mobile|MQQBrowser)+/.test(window.navigator.userAgent) ? 'mobile' : 'pc',
        shell: /.*Lark.*/.test(window.navigator.userAgent) ? 'lark' : /.*DingTalk.*/.test(window.navigator.userAgent) ? 'dingtalk' : 'other'
      },
      // 过滤掉这个 在网络慢的情况下 会 Failed to fetch 的,避免被监控到然后上报告警
      filterRule: [
        {
          _event_info_: /log_manage\/web/
        }
      ]
    })
    // const log = new TeacherLog({
    //   trackInstance: window.$track,  //  自己系统里的日志插件实例，如果没有传入将不会上报
    //   justPrint: "<%= VUE_APP_SLS_NODE_ENV %>" === 'dev', // 仅打印在控制台不上报的条件 不传默认true不上报
    //   extraTrackInfo: '', // 需要上报的额外附加信息 接受 string 会加在最前面
    // });
    // window.Log = log.debuggerTrack;
  </script>
  <script type="module">
    import TeacherLog from "https://hl-common-prod.obs.cn-east-3.myhuaweicloud.com/hrjy/track/teacherLog.js";

    const getLogPlatform = () => {
      if (
        /AppleWebKit.*(Mobile|MQQBrowser)+/.test(window.navigator.userAgent)
      ) {
        if (/.*Lark.*/.test(window.navigator.userAgent)) {
          return "h5-lark";
        }
        if (/.*DingTalk.*/.test(window.navigator.userAgent)) {
          return "h5-DingTalk";
        }
        return "h5-other";
      }
      return "pc";
    };

    const teacherLogInstance = new TeacherLog({
      trackInstance: window.$track,
      env: "<%= VUE_APP_TEACHER_LOG_ENV %>",
      type: "web", // web | desktop | mobile
      appCode: "hr",
      platform: getLogPlatform(),
      uuid: localStorage.getItem("uuid"), // new WebTrack 会生成的
      extraRowInfo: { ua: window.navigator.userAgent },
    });

    window.LogInstance = teacherLogInstance;
    window.Log = teacherLogInstance.debuggerTrack;
  </script>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <script type="text/javascript"
    src="<%= VUE_APP_SLS_NODE_ENV==='production'? 'https://hl-common-prod.obs.cn-east-3.myhuaweicloud.com/hrjy/webStatic/vue.min.js' : 'https://hl-test-ca.obs.cn-east-3.myhuaweicloud.com/hrjy/webStatic/vue.min.js' %>"></script>
  <!-- built files will be auto injected -->
  <script type="text/javascript"
    src="<%= VUE_APP_SLS_NODE_ENV==='production'? 'https://hl-common-prod.obs.cn-east-3.myhuaweicloud.com/hrjy/webStatic/aliyun-oss-sdk.min.js' : 'https://hl-test-ca.obs.cn-east-3.myhuaweicloud.com/hrjy/webStatic/aliyun-oss-sdk.min.js' %>"
    defer></script>
  <script type="text/javascript"
    src="<%= VUE_APP_SLS_NODE_ENV==='production'? 'https://hl-common-prod.obs.cn-east-3.myhuaweicloud.com/hrjy/webStatic/html2canvas.min.js' : 'https://hl-test-ca.obs.cn-east-3.myhuaweicloud.com/hrjy/webStatic/html2canvas.min.js' %>"
    defer></script>
  <script type="text/javascript"
    src="<%= VUE_APP_SLS_NODE_ENV==='production'? 'https://hl-common-prod.obs.cn-east-3.myhuaweicloud.com/hrjy/webStatic/jquery.min.js' : 'https://hl-test-ca.obs.cn-east-3.myhuaweicloud.com/hrjy/webStatic/jquery.min.js' %>"
    defer></script>
  <script type="text/javascript"
    src="<%= VUE_APP_SLS_NODE_ENV==='production'? 'https://hl-common-prod.obs.cn-east-3.myhuaweicloud.com/hrjy/webStatic/wangEditor.min.js' : 'https://hl-test-ca.obs.cn-east-3.myhuaweicloud.com/hrjy/webStatic/wangEditor.min.js' %>"
    defer></script>

</body>

</html>