class CustomAudioProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    this.frameCount = 0;
    this.sampleBuffer = [];
    this.active = true; // 是否活跃
    this.enablePCM = false; // 是否转换为16位PCM

    // 监听来自主线程的消息
    this.port.onmessage = event => {
      // 更新 active 状态
      if (Object.prototype.hasOwnProperty.call(event.data, 'active')) {
        this.active = event.data.active;
      }
      // 更新 enablePCM
      // if (event.data.hasOwnProperty("enablePCM")) {
      //   this.enablePCM = event.data.enablePCM;
      // }
    };
  }

  /**
   * 转换为16位PCM
   * @param {number} analogueSignal - 模拟信号
   * @returns {number} 16位PCM采样数据
   */
  pcmConvert(analogueSignal) {
    // 将模拟信号转换为16位PCM采样数据
    // 这里使用了Number.MAX_SAFE_INTEGER 和 Number.MIN_SAFE_INTEGER
    // 这样可以避免超出Number.MAX_VALUE或者Number.MIN_VALUE的问题
    // 详细可以参考:
    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER
    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MIN_SAFE_INTEGER
    return Math.max(
      Number.MIN_SAFE_INTEGER,
      Math.min(Number.MAX_SAFE_INTEGER, Math.round(analogueSignal * 32767))
    );
  }

  processChannelData(...args) {
    args.forEach(sample => {
      if (this.enablePCM) {
        this.sampleBuffer.push(this.pcmConvert(sample));
      } else {
        this.sampleBuffer.push(sample);
      }
    });
  }

  process(inputs, outputs) {
    try {
      if (!this.active) {
        return true; // 如果不活跃，则不处理音频数据但保持节点运行
      }

      const input = inputs[0];
      const output = outputs[0];

      // 复制输入到输出，并收集数据
      // input.forEach((channelData, channelIndex) => {
      //   /**
      //    * 在采集音频是双声道的情况下 左右声道数据结构为：
      //    * 左声道-input[0]
      //    * 右声道-input[1]
      //    */

      //   const outputChannel = output[channelIndex];
      //   for (let i = 0; i < channelData.length; i++) {
      //     outputChannel[i] = channelData[i]; // 直接将输入复制到输出
      //     // 转换为16位PCM并存储
      //     // PCM(Pulse Code Modulation)指的是脉冲编码调制,是一种将模拟信号(如音频)转换为数字信号的编码方式
      //     // this.sampleBuffer.push(Math.floor(channelData[i] * 32767));
      //     // this.sampleBuffer.push(channelData[i] * 32767);
      //     // 在 AudioWorklet 中修正转换
      //     this.sampleBuffer.push(
      //       Math.max(-32768, Math.min(32767, Math.round(channelData[i] * 32767)))
      //     );
      //   }
      // });

      // input.forEach((channelData, channelIndex) => {
      //   /**
      //    * 在采集音频是双声道的情况下 左右声道数据结构为：
      //    * 左声道-input[0]
      //    * 右声道-input[1]
      //    */

      //   const outputChannel = output[channelIndex];
      //   for (let i = 0; i < channelData.length; i++) {
      // /**
      //  * 输入到输出需要逐个复制每个样本 而不是单纯的复制数组引用
      //  * 错误示范: output[0] = input[0]; output[1] = input[1];
      //  * NOTE:实时音频数据处理要求在样本级别上进行操作 所以要遍历每个样本
      //  *  */
      //     outputChannel[i] = channelData[i];
      //   }
      // });

      // const [leftInput, rightInput] = input;
      // // 将原始样本数据存储到数组中;
      // for (let i = 0; i < leftInput.length; i++) {
      //   // NOTE:左右声道数据交错存储 这样音频质量更高
      //   this.sampleBuffer.push(
      //     this.pcmConvert(leftInput[i]),
      //     this.pcmConvert(rightInput[i])
      //   );
      // }

      // 遍历样本数据 1.复制输入到输出 2.交错存储左右声道数据
      // input.forEach((channelData, channelIndex) => {
      //   const outputChannel = output[channelIndex];

      //   // 如果输入是双声道，则保证channelIndex为0或1
      //   if (channelIndex > 1) return; // 确保只处理前两个声道（双声道）

      //   for (let i = 0; i < channelData.length; i++) {
      //     // 直接将输入复制到输出
      //     outputChannel[i] = channelData[i];

      //     // 当处理第一个声道时，同时收集第二个声道的数据（如果存在）
      //     if (channelIndex === 0) {
      //       const rightInput = input[1]; // 确保第二个声道存在
      //       if (rightInput && i < rightInput.length) {
      //         // NOTE:左右声道数据交错存储 这种存储方式使音频质量更高
      //         this.processChannelData(channelData[i], rightInput[i]);
      //       } else {
      //         // 如果只有一个声道或右声道数据不足，仅处理左声道
      //         this.processChannelData(channelData[i]);
      //       }
      //     }
      //   }
      // });

      // -------------------- 处理多声道 start --------------------
      input.forEach((channelData, channelIndex) => {
        const outputChannel = output[channelIndex];

        // 确保只处理存在的声道
        if (channelIndex >= output.length) return;

        /**
         * 输入到输出需要逐个复制每个样本 而不是单纯的复制数组引用
         * 错误示范: output[0] = input[0]; output[1] = input[1];
         * NOTE:实时音频数据处理要求在样本级别上进行操作 所以要遍历每个样本
         *  */
        for (let i = 0; i < channelData.length; i++) {
          // 直接将输入复制到输出
          outputChannel[i] = channelData[i];
        }
      });

      // 交错存储所有声道的数据
      const bufferLength = input[0].length;
      // const numChannels = input.length;
      // NOTE: 注意声道数别错了
      // const numChannels = output.length;
      // NOTE:目前只处理单声道
      const numChannels = 1;
      // NOTE:目前只处理双声道
      // const numChannels = 2;

      for (let i = 0; i < bufferLength; i++) {
        for (let channel = 0; channel < numChannels; channel++) {
          const channelData = input[channel];
          if (channelData && i < channelData.length) {
            this.processChannelData(channelData[i]);
          }
        }
      }

      // -------------------- 处理多声道 end --------------------

      // 每 4096 帧发送一次数据
      this.frameCount += input[0].length;
      if (this.frameCount >= 4096) {
        // 发送数据到主线程
        this.port.postMessage({
          audioData: this.sampleBuffer.slice(),
          input: input,
          inputs: inputs,
          outputs: outputs
        });
        this.sampleBuffer = [];
        this.frameCount = 0;
      }

      return true;
    } catch (error) {
      // 发送错误信息到主线程
      this.port.postMessage({
        error: error.message
      });
      return false; // 返回false表示处理失败
    }
  }
}

registerProcessor('custom-audio-processor', CustomAudioProcessor);
