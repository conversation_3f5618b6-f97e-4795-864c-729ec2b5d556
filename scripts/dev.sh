#!/bin/bash

# estudy-room 项目启动脚本
# 自动处理 Node.js 版本切换并启动开发服务器

# 清理 Volta 环境冲突
unset npm_config_prefix VOLTA_HOME 2>/dev/null || true
export PATH=$(echo $PATH | tr ':' '\n' | grep -v "volta" | tr '\n' ':' | sed 's/:$//')

# 加载 nvm
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && source "$NVM_DIR/nvm.sh"
[ -s "/usr/local/opt/nvm/nvm.sh" ] && source "/usr/local/opt/nvm/nvm.sh"
[ -s "/opt/homebrew/opt/nvm/nvm.sh" ] && source "/opt/homebrew/opt/nvm/nvm.sh"

# 获取项目要求的 Node.js 版本
NODE_VERSION=$(cat .nvmrc 2>/dev/null || echo "12")

# 安装并切换版本
echo "🔄 切换到 Node.js $NODE_VERSION..."
nvm install $NODE_VERSION >/dev/null 2>&1 || true
nvm use $NODE_VERSION

# 显示当前版本
echo "✅ 当前版本: $(node --version)"

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 启动开发服务器
echo "🚀 启动开发服务器..."
exec vue-cli-service serve